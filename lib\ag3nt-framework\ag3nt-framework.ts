/**
 * AG3NT Framework - Main Framework Class
 * 
 * The core orchestrator for the AG3NT autonomous agent framework.
 * Provides a unified interface for:
 * - Agent registration and management
 * - Context engine integration
 * - Workflow orchestration
 * - Multi-agent coordination
 * - Session management
 * 
 * This framework rivals CrewAI and LangGraph with superior capabilities:
 * - MCP-enhanced agents with real-time context
 * - Sequential thinking capabilities
 * - Context enrichment pipeline
 * - Advanced agent coordination
 * - Built-in progress tracking
 */

import { EventEmitter } from "events"
import { BaseAgent, AgentState } from "./core/base-agent"
import { AgentRegistry, RegisteredAgent } from "./core/agent-registry"
import { AgentCommunicationProtocol } from "./core/agent-communication"
import { WorkflowCoordinator } from "./core/workflow-coordinator"
import { UnifiedContextEngine } from "../unified-context-engine-v2"
import { PlanningAgent } from "./agents/planning-agent"
import { AdvancedFeaturesManager, type AdvancedFeaturesConfig } from "./advanced"
import { TaskDelegationSystem } from "./coordination/task-delegation-system"
import { ConsensusProtocolEngine } from "./coordination/consensus-protocol-engine"
import { WorkflowHandoffManager } from "./coordination/workflow-handoff-manager"
import { CoordinationPatternRegistry } from "./coordination/coordination-pattern-registry"
import { AgentDiscoveryService } from "./discovery/agent-discovery-service"
import { LoadBalancer } from "./discovery/load-balancer"
import { FailoverManager } from "./discovery/failover-manager"

export interface FrameworkConfig {
  contextEngine?: {
    enableMCP?: boolean
    enableSequentialThinking?: boolean
    enableRAG?: boolean
    enableContextEnrichment?: boolean
  }
  agents?: {
    maxConcurrentSessions?: number
    defaultTimeout?: number
    defaultRetries?: number
  }
  monitoring?: {
    enableHealthChecks?: boolean
    healthCheckInterval?: number
    enableMetrics?: boolean
  }
  coordination?: {
    enableTaskDelegation?: boolean
    enableConsensus?: boolean
    enableWorkflowHandoffs?: boolean
    enablePatternRegistry?: boolean
    delegationTimeout?: number
    consensusTimeout?: number
    handoffTimeout?: number
  }
  discovery?: {
    enableAgentDiscovery?: boolean
    enableLoadBalancing?: boolean
    enableFailover?: boolean
    discoveryInterval?: number
    healthCheckInterval?: number
    loadBalancingAlgorithm?: 'round_robin' | 'weighted_round_robin' | 'least_connections' | 'adaptive'
  }
  advancedFeatures?: AdvancedFeaturesConfig
}

export interface SessionConfig {
  sessionId?: string
  priority?: 'low' | 'medium' | 'high' | 'critical'
  tags?: string[]
  timeout?: number
  agentPreferences?: {
    agentType?: string
    agentId?: string
  }
}

export interface ExecutionResult {
  sessionId: string
  agentId: string
  agentType: string
  success: boolean
  result?: AgentState
  error?: string
  duration: number
  metadata: {
    startTime: string
    endTime: string
    stepsCompleted: number
    totalSteps: number
  }
}

/**
 * AG3NT Framework - The autonomous agent orchestration framework
 */
export class AG3NTFramework extends EventEmitter {
  private registry: AgentRegistry
  private communication: AgentCommunicationProtocol
  private workflowCoordinator: WorkflowCoordinator
  private contextEngine: UnifiedContextEngine | null = null
  private advancedFeatures?: AdvancedFeaturesManager

  // Enhanced coordination systems
  private delegationSystem?: TaskDelegationSystem
  private consensusEngine?: ConsensusProtocolEngine
  private handoffManager?: WorkflowHandoffManager
  private patternRegistry?: CoordinationPatternRegistry

  // Discovery and load balancing systems
  private discoveryService?: AgentDiscoveryService
  private loadBalancer?: LoadBalancer
  private failoverManager?: FailoverManager

  private config: FrameworkConfig
  private isInitialized: boolean = false
  private activeSessions: Map<string, {
    agentId: string
    startTime: Date
    config: SessionConfig
  }> = new Map()

  constructor(config: FrameworkConfig = {}) {
    super()
    
    this.config = {
      contextEngine: {
        enableMCP: true,
        enableSequentialThinking: true,
        enableRAG: true,
        enableContextEnrichment: true,
        ...config.contextEngine
      },
      agents: {
        maxConcurrentSessions: 10,
        defaultTimeout: 300000, // 5 minutes
        defaultRetries: 3,
        ...config.agents
      },
      monitoring: {
        enableHealthChecks: true,
        healthCheckInterval: 30000, // 30 seconds
        enableMetrics: true,
        ...config.monitoring
      },
      coordination: {
        enableTaskDelegation: true,
        enableConsensus: true,
        enableWorkflowHandoffs: true,
        enablePatternRegistry: true,
        delegationTimeout: 300000, // 5 minutes
        consensusTimeout: 300000, // 5 minutes
        handoffTimeout: 300000, // 5 minutes
        ...config.coordination
      },
      discovery: {
        enableAgentDiscovery: true,
        enableLoadBalancing: true,
        enableFailover: true,
        discoveryInterval: 30000, // 30 seconds
        healthCheckInterval: 10000, // 10 seconds
        loadBalancingAlgorithm: 'adaptive',
        ...config.discovery
      },
      advancedFeatures: {
        adaptiveLearning: { enabled: true },
        temporalDatabase: { enabled: true },
        collaboration: { enabled: true },
        optimization: { enabled: true },
        marketplace: { enabled: true },
        monitoring: { enabled: true },
        ...config.advancedFeatures
      }
    }

    this.registry = new AgentRegistry()
    this.communication = new AgentCommunicationProtocol()
    this.workflowCoordinator = new WorkflowCoordinator(this.communication, this.registry)
  }

  /**
   * Initialize the framework
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return

    console.log('🚀 Initializing AG3NT Framework...')

    // Initialize communication protocol
    await this.communication.initialize()

    // Initialize agent registry
    await this.registry.initialize()

    // Initialize workflow coordinator
    await this.workflowCoordinator.initialize()

    // Initialize context engine if enabled
    if (this.config.contextEngine?.enableMCP) {
      this.contextEngine = new UnifiedContextEngine({
        enableMCP: true,
        enableSequentialThinking: this.config.contextEngine.enableSequentialThinking || true,
        enableRAG: this.config.contextEngine.enableRAG || true,
        enableContextEnrichment: this.config.contextEngine.enableContextEnrichment || true,
        enableCodebaseAnalysis: true,
        enableNeo4j: true,
        enableRedis: true,
        enableTemporalGraph: true,
        memoryDepth: 1000,
        validationLevel: 'strict',
        cacheSize: 10000,
        ttlDefault: 3600000
      })
      await this.contextEngine.initialize()
      console.log('🧠 Unified Context Engine (MasterMold) initialized with full capabilities')
    }

    // Initialize coordination systems
    if (this.config.coordination?.enableTaskDelegation) {
      this.delegationSystem = new TaskDelegationSystem({
        enableHierarchicalDelegation: true,
        enablePeerDelegation: true,
        maxDelegationDepth: 3,
        delegationTimeout: this.config.coordination.delegationTimeout || 300000,
        requireConfirmation: true,
        enableRollback: true
      })
      console.log('🤝 Task Delegation System initialized')
    }

    if (this.config.coordination?.enableConsensus) {
      this.consensusEngine = new ConsensusProtocolEngine({
        defaultProtocol: 'majority',
        quorumThreshold: 0.6,
        votingTimeout: this.config.coordination.consensusTimeout || 300000,
        enableDelegatedVoting: true,
        enableVetoRights: false,
        conflictResolutionStrategy: 'mediation'
      })
      console.log('🗳️ Consensus Protocol Engine initialized')
    }

    if (this.config.coordination?.enableWorkflowHandoffs) {
      this.handoffManager = new WorkflowHandoffManager({
        enableStateValidation: true,
        enableRollback: true,
        handoffTimeout: this.config.coordination.handoffTimeout || 300000,
        requireConfirmation: true,
        enableCheckpoints: true,
        maxRetries: 3
      })
      console.log('🔄 Workflow Handoff Manager initialized')
    }

    if (this.config.coordination?.enablePatternRegistry) {
      this.patternRegistry = new CoordinationPatternRegistry()
      console.log('📋 Coordination Pattern Registry initialized')
    }

    // Initialize discovery and load balancing systems
    if (this.config.discovery?.enableAgentDiscovery) {
      this.discoveryService = new AgentDiscoveryService({
        enableAutoDiscovery: true,
        discoveryInterval: this.config.discovery.discoveryInterval || 30000,
        healthCheckInterval: this.config.discovery.healthCheckInterval || 10000,
        maxRetries: 3,
        timeoutMs: 5000,
        enableServiceMesh: false,
        enableLoadBalancing: this.config.discovery.enableLoadBalancing || true
      })
      await this.discoveryService.start()
      console.log('🔍 Agent Discovery Service initialized')
    }

    if (this.config.discovery?.enableLoadBalancing && this.discoveryService) {
      this.loadBalancer = new LoadBalancer(this.discoveryService, {
        algorithm: this.config.discovery.loadBalancingAlgorithm || 'adaptive',
        enableHealthChecks: true,
        enableCircuitBreaker: true,
        enableStickySessions: false,
        maxRetries: 3,
        retryDelay: 1000,
        circuitBreakerThreshold: 5,
        circuitBreakerTimeout: 30000,
        adaptiveWindowSize: 100
      })
      console.log('⚖️ Load Balancer initialized')
    }

    if (this.config.discovery?.enableFailover && this.discoveryService && this.loadBalancer) {
      this.failoverManager = new FailoverManager(this.discoveryService, this.loadBalancer, {
        enableAutoFailover: true,
        enableGracefulShutdown: true,
        failureDetectionInterval: 5000,
        maxFailureThreshold: 3,
        recoveryTimeout: 30000,
        backupAgentRatio: 0.5,
        enableDataReplication: true,
        enableStateTransfer: true
      })
      this.failoverManager.start()
      console.log('🛡️ Failover Manager initialized')
    }

    // Initialize advanced features
    if (this.config.advancedFeatures) {
      this.advancedFeatures = new AdvancedFeaturesManager(this.config.advancedFeatures)
      await this.advancedFeatures.initialize()
      console.log('🚀 Advanced Features initialized')
    }

    // Register built-in agents
    await this.registerBuiltInAgents()

    this.isInitialized = true
    this.emit('framework_initialized')
    console.log('✅ AG3NT Framework initialized successfully with advanced features')
  }

  /**
   * Register a custom agent with the framework
   */
  async registerAgent(agent: BaseAgent, metadata?: any): Promise<string> {
    if (!this.isInitialized) {
      throw new Error('Framework must be initialized before registering agents')
    }

    // Initialize agent with context engine
    if (this.contextEngine) {
      await agent.initialize(this.contextEngine)
      // Register agent with context engine for enhanced context management
      await this.contextEngine.registerAgent({
        agentType: agent.type as any,
        operationId: `op-${Date.now()}`,
        requiredCapabilities: agent['config'].capabilities.requiredCapabilities,
        contextFilters: agent['config'].capabilities.contextFilters
      })
    }

    // Register with communication protocol
    await this.communication.registerAgent(agent.id, agent.type, agent['config'].capabilities.requiredCapabilities)

    // Register with registry
    const agentId = await this.registry.registerAgent(agent, metadata)

    // Register with discovery service if enabled
    if (this.discoveryService) {
      await this.discoveryService.registerAgent({
        agentId: agent.id,
        agentType: agent.type,
        version: '1.0.0',
        capabilities: agent['config']?.capabilities?.requiredCapabilities?.map((cap: any) => ({
          name: cap.name || cap,
          version: '1.0.0',
          proficiency: cap.proficiency || 0.8,
          maxConcurrency: cap.maxConcurrency || 5,
          estimatedLatency: cap.estimatedLatency || 1000,
          resourceRequirements: {
            cpu: 1,
            memory: 512,
            network: 100,
            storage: 100
          }
        })) || [],
        endpoint: `agent://${agent.id}`,
        metadata: {
          hostname: 'localhost',
          region: 'local',
          zone: 'default',
          environment: 'development',
          tags: metadata?.tags || [],
          customProperties: metadata || {}
        }
      })
    }

    // Create failover plan if enabled
    if (this.failoverManager) {
      try {
        await this.failoverManager.createFailoverPlan(agent.id)
      } catch (error) {
        console.warn(`Failed to create failover plan for agent ${agent.id}:`, error)
      }
    }

    this.emit('agent_registered', { agentId, agentType: agent.type })
    return agentId
  }

  /**
   * Execute a task using the best available agent
   */
  async execute(
    agentType: string,
    input: any,
    config: SessionConfig = {}
  ): Promise<ExecutionResult> {
    if (!this.isInitialized) {
      throw new Error('Framework must be initialized before execution')
    }

    const startTime = new Date()
    const sessionId = config.sessionId || `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`

    try {
      // Find the best agent for the task
      const agent = this.findBestAgent(agentType, config)
      if (!agent) {
        throw new Error(`No available agent found for type: ${agentType}`)
      }

      console.log(`🎯 Executing ${agentType} task with agent ${agent.agentId}`)

      // Track session
      this.activeSessions.set(sessionId, {
        agentId: agent.agentId,
        startTime,
        config
      })

      // Execute the task
      const result = await agent.instance.execute(input, {
        sessionId,
        priority: config.priority,
        tags: config.tags
      })

      const endTime = new Date()
      const duration = endTime.getTime() - startTime.getTime()

      // Clean up session
      this.activeSessions.delete(sessionId)

      const executionResult: ExecutionResult = {
        sessionId,
        agentId: agent.agentId,
        agentType: agent.agentType,
        success: true,
        result,
        duration,
        metadata: {
          startTime: startTime.toISOString(),
          endTime: endTime.toISOString(),
          stepsCompleted: result.metadata.completedSteps,
          totalSteps: result.metadata.totalSteps
        }
      }

      this.emit('execution_completed', executionResult)
      return executionResult

    } catch (error) {
      const endTime = new Date()
      const duration = endTime.getTime() - startTime.getTime()

      // Clean up session
      this.activeSessions.delete(sessionId)

      const executionResult: ExecutionResult = {
        sessionId,
        agentId: '',
        agentType,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        duration,
        metadata: {
          startTime: startTime.toISOString(),
          endTime: endTime.toISOString(),
          stepsCompleted: 0,
          totalSteps: 0
        }
      }

      this.emit('execution_failed', executionResult)
      throw error
    }
  }

  /**
   * Get framework statistics
   */
  getStats(): {
    framework: {
      initialized: boolean
      activeSessions: number
      totalAgents: number
    }
    agents: ReturnType<AgentRegistry['getStats']>
    communication: ReturnType<AgentCommunicationProtocol['getStats']>
    workflows: ReturnType<WorkflowCoordinator['getStats']>
    contextEngine: {
      enabled: boolean
      mcpEnabled: boolean
      ragEnabled: boolean
      stats?: ReturnType<AG3NTContextEngine['getStats']>
    }
  } {
    return {
      framework: {
        initialized: this.isInitialized,
        activeSessions: this.activeSessions.size,
        totalAgents: this.registry.getStats().totalAgents
      },
      agents: this.registry.getStats(),
      communication: this.communication.getStats(),
      workflows: this.workflowCoordinator.getStats(),
      contextEngine: {
        enabled: !!this.contextEngine,
        mcpEnabled: !!this.config.contextEngine?.enableMCP,
        ragEnabled: !!this.config.contextEngine?.enableRAG,
        stats: this.contextEngine?.getStats()
      }
    }
  }

  /**
   * Get all registered agents
   */
  getAgents(): RegisteredAgent[] {
    return this.registry.findAgents()
  }

  /**
   * Get agent by ID
   */
  getAgent(agentId: string): RegisteredAgent | null {
    return this.registry.getAgent(agentId)
  }

  /**
   * Shutdown the framework
   */
  async shutdown(): Promise<void> {
    console.log('🛑 Shutting down AG3NT Framework...')

    // Cancel active sessions
    this.activeSessions.clear()

    // Shutdown workflow coordinator
    await this.workflowCoordinator.shutdown()

    // Shutdown registry
    await this.registry.shutdown()

    // Shutdown communication protocol
    await this.communication.shutdown()

    // Shutdown context engine
    if (this.contextEngine) {
      await this.contextEngine.shutdown()
      this.contextEngine = null
    }

    this.isInitialized = false
    this.emit('framework_shutdown')
    console.log('✅ AG3NT Framework shutdown complete')
  }

  /**
   * Register built-in agents
   */
  private async registerBuiltInAgents(): Promise<void> {
    // Import all agent classes
    const { PlanningAgent } = await import('./agents/planning-agent')
    const { TaskPlannerAgent } = await import('./agents/task-planner-agent')
    const { ExecutorAgent } = await import('./agents/executor-agent')
    const { FrontendCoderAgent } = await import('./agents/frontend-coder-agent')
    const { BackendCoderAgent } = await import('./agents/backend-coder-agent')
    const { TesterAgent } = await import('./agents/tester-agent')
    const { ReviewerAgent } = await import('./agents/reviewer-agent')
    const { WorkflowAgent } = await import('./agents/workflow-agent')
    const { ContextEngineAgent } = await import('./agents/context-engine-agent')
    const { DocumentationAgent } = await import('./agents/documentation-agent')
    const { DevOpsAgent } = await import('./agents/devops-agent')
    const { SecurityAgent } = await import('./agents/security-agent')
    const { MaintenanceAgent } = await import('./agents/maintenance-agent')
    const { IntegrationAgent } = await import('./agents/integration-agent')
    const { AnalyticsAgent } = await import('./agents/analytics-agent')
    const { UserInteractionAgent } = await import('./agents/user-interaction-agent')

    // Register core agents
    const planningAgent = new PlanningAgent()
    await this.registerAgent(planningAgent, {
      description: 'Sophisticated project planning agent with MCP enhancement',
      tags: ['planning', 'analysis', 'architecture'],
      priority: 10
    })

    const contextEngineAgent = new ContextEngineAgent()
    await this.registerAgent(contextEngineAgent, {
      description: 'Deep codebase understanding and context intelligence',
      tags: ['context', 'intelligence', 'analysis', 'neural-network'],
      priority: 10
    })

    const taskPlannerAgent = new TaskPlannerAgent()
    await this.registerAgent(taskPlannerAgent, {
      description: 'Task decomposition and project planning agent',
      tags: ['task-planning', 'decomposition', 'scheduling'],
      priority: 9
    })

    const executorAgent = new ExecutorAgent()
    await this.registerAgent(executorAgent, {
      description: 'Task execution and coordination agent',
      tags: ['execution', 'coordination', 'delegation'],
      priority: 8
    })

    const workflowAgent = new WorkflowAgent()
    await this.registerAgent(workflowAgent, {
      description: 'Multi-agent workflow orchestration agent',
      tags: ['workflow', 'orchestration', 'coordination'],
      priority: 9
    })

    // Register development agents
    const frontendCoderAgent = new FrontendCoderAgent()
    await this.registerAgent(frontendCoderAgent, {
      description: 'Frontend development and UI implementation agent',
      tags: ['frontend', 'ui', 'react', 'vue', 'angular'],
      priority: 8
    })

    const backendCoderAgent = new BackendCoderAgent()
    await this.registerAgent(backendCoderAgent, {
      description: 'Backend development and API implementation agent',
      tags: ['backend', 'api', 'database', 'microservices'],
      priority: 8
    })

    const testerAgent = new TesterAgent()
    await this.registerAgent(testerAgent, {
      description: 'Comprehensive testing and quality assurance agent',
      tags: ['testing', 'qa', 'automation', 'coverage'],
      priority: 7
    })

    const reviewerAgent = new ReviewerAgent()
    await this.registerAgent(reviewerAgent, {
      description: 'Code review and quality assessment agent',
      tags: ['review', 'quality', 'security', 'compliance'],
      priority: 7
    })

    // Register specialized agents
    const documentationAgent = new DocumentationAgent()
    await this.registerAgent(documentationAgent, {
      description: 'Technical documentation management and generation',
      tags: ['documentation', 'technical-writing', 'api-docs', 'guides'],
      priority: 6
    })

    const devopsAgent = new DevOpsAgent()
    await this.registerAgent(devopsAgent, {
      description: 'CI/CD pipeline and infrastructure automation',
      tags: ['devops', 'ci-cd', 'infrastructure', 'deployment'],
      priority: 7
    })

    const securityAgent = new SecurityAgent()
    await this.registerAgent(securityAgent, {
      description: 'Security scanning and compliance management',
      tags: ['security', 'vulnerability', 'compliance', 'audit'],
      priority: 8
    })

    const maintenanceAgent = new MaintenanceAgent()
    await this.registerAgent(maintenanceAgent, {
      description: 'Code maintenance and dependency management',
      tags: ['maintenance', 'dependencies', 'refactoring', 'debt'],
      priority: 6
    })

    const integrationAgent = new IntegrationAgent()
    await this.registerAgent(integrationAgent, {
      description: 'External API and service integration',
      tags: ['integration', 'api', 'connectivity', 'interoperability'],
      priority: 7
    })

    const analyticsAgent = new AnalyticsAgent()
    await this.registerAgent(analyticsAgent, {
      description: 'System monitoring and analytics intelligence',
      tags: ['analytics', 'monitoring', 'telemetry', 'insights'],
      priority: 7
    })

    const userInteractionAgent = new UserInteractionAgent()
    await this.registerAgent(userInteractionAgent, {
      description: 'Natural language user interface and interaction',
      tags: ['user-interaction', 'nlp', 'communication', 'feedback'],
      priority: 8
    })

    console.log('📋 All specialized agents registered (15 agents)')
  }

  /**
   * Find the best agent for a task
   */
  private findBestAgent(agentType: string, config: SessionConfig): RegisteredAgent | null {
    // If specific agent requested
    if (config.agentPreferences?.agentId) {
      const agent = this.registry.getAgent(config.agentPreferences.agentId)
      if (agent && agent.agentType === agentType) {
        return agent
      }
    }

    // Find best available agent
    return this.registry.getBestAgent({
      agentType,
      priority: config.priority === 'critical' ? 10 : 
                config.priority === 'high' ? 7 :
                config.priority === 'medium' ? 5 : 1,
      tags: config.tags
    })
  }

  /**
   * Get advanced features manager
   */
  getAdvancedFeatures(): AdvancedFeaturesManager | undefined {
    return this.advancedFeatures
  }

  /**
   * Get adaptive learning system
   */
  getAdaptiveLearning() {
    return this.advancedFeatures?.getAdaptiveLearning()
  }

  /**
   * Get temporal database
   */
  getTemporalDatabase() {
    return this.advancedFeatures?.getTemporalDatabase()
  }

  /**
   * Get collaboration system
   */
  getCollaboration() {
    return this.advancedFeatures?.getCollaboration()
  }

  /**
   * Get optimization system
   */
  getOptimization() {
    return this.advancedFeatures?.getOptimization()
  }

  /**
   * Get marketplace
   */
  getMarketplace() {
    return this.advancedFeatures?.getMarketplace()
  }

  /**
   * Get monitoring system
   */
  getMonitoring() {
    return this.advancedFeatures?.getMonitoring()
  }

  /**
   * Enhanced coordination methods
   */

  /**
   * Get task delegation system
   */
  getDelegationSystem(): TaskDelegationSystem | undefined {
    return this.delegationSystem
  }

  /**
   * Get consensus protocol engine
   */
  getConsensusEngine(): ConsensusProtocolEngine | undefined {
    return this.consensusEngine
  }

  /**
   * Get workflow handoff manager
   */
  getHandoffManager(): WorkflowHandoffManager | undefined {
    return this.handoffManager
  }

  /**
   * Get coordination pattern registry
   */
  getPatternRegistry(): CoordinationPatternRegistry | undefined {
    return this.patternRegistry
  }

  /**
   * Delegate task between agents
   */
  async delegateTask(
    fromAgent: string,
    toAgent: string,
    task: any,
    delegationType: 'hierarchical' | 'peer' | 'emergency' | 'load_balance' = 'hierarchical'
  ): Promise<any> {
    if (!this.delegationSystem) {
      throw new Error('Task delegation system not enabled')
    }

    return await this.delegationSystem.delegateTask(
      fromAgent,
      {
        taskId: `task-${Date.now()}`,
        type: task.type || 'general',
        description: task.description || 'Framework delegated task',
        requirements: task.requirements || [],
        constraints: task.constraints || [],
        priority: task.priority || 'medium',
        context: task,
        dependencies: task.dependencies || [],
        expectedOutput: task.expectedOutput
      },
      delegationType,
      toAgent
    )
  }

  /**
   * Create consensus proposal
   */
  async createConsensusProposal(
    proposerId: string,
    title: string,
    description: string,
    options: any[]
  ): Promise<any> {
    if (!this.consensusEngine) {
      throw new Error('Consensus protocol engine not enabled')
    }

    return await this.consensusEngine.submitProposal({
      proposerId,
      title,
      description,
      type: 'decision',
      options: options.map((option, index) => ({
        optionId: `option-${index}`,
        title: option.title || `Option ${index + 1}`,
        description: option.description || '',
        impact: {
          scope: option.scope || [],
          magnitude: option.magnitude || 'moderate',
          reversibility: option.reversibility !== false,
          timeframe: option.timeframe || 'immediate',
          dependencies: option.dependencies || []
        },
        cost: option.cost || 0,
        risk: option.risk || 'medium',
        feasibility: option.feasibility || 0.8,
        supportingData: option.data || {}
      }))
    })
  }

  /**
   * Initiate workflow handoff
   */
  async initiateHandoff(
    fromAgent: string,
    toAgent: string,
    workflowId: string,
    taskId: string,
    state: any
  ): Promise<any> {
    if (!this.handoffManager) {
      throw new Error('Workflow handoff manager not enabled')
    }

    return await this.handoffManager.initiateHandoff(
      fromAgent,
      toAgent,
      workflowId,
      taskId,
      {
        stateId: `state-${Date.now()}`,
        version: 1,
        data: state,
        metadata: {
          lastModified: Date.now(),
          modifiedBy: fromAgent,
          size: JSON.stringify(state).length,
          encoding: 'utf-8',
          format: 'json',
          schema: 'workflow-state-v1'
        },
        dependencies: [],
        artifacts: [],
        checksum: JSON.stringify(state).length.toString(36)
      }
    )
  }

  /**
   * Register agent for coordination
   */
  registerAgentForCoordination(agentId: string, capabilities: any[], authority: number = 5): void {
    // Register with delegation system
    if (this.delegationSystem) {
      this.delegationSystem.registerAgent(agentId, {
        authorityLevel: authority,
        capabilities: capabilities.map(cap => ({
          name: cap.name || cap,
          proficiency: cap.proficiency || 0.8,
          experience: cap.experience || 1,
          lastUsed: Date.now(),
          successRate: cap.successRate || 0.9,
          averageTime: cap.averageTime || 30000
        })),
        delegationRights: [{
          canDelegateTo: ['*'],
          maxAuthorityLevel: authority - 1,
          allowedTaskTypes: ['*'],
          requiresApproval: authority < 7
        }],
        maxLoad: 10,
        trustScore: 0.8,
        specializations: capabilities.map(cap => cap.name || cap)
      })
    }

    // Register with consensus engine
    if (this.consensusEngine) {
      this.consensusEngine.registerVoter(agentId, {
        weight: authority / 10,
        expertise: capabilities.map(cap => cap.name || cap),
        authority,
        canPropose: authority >= 5,
        canVeto: authority >= 8,
        canDelegate: true,
        trustScore: 0.8
      })
    }

    console.log(`🤖 Registered agent ${agentId} for enhanced coordination`)
  }

  /**
   * Get coordination analytics
   */
  getCoordinationAnalytics(): any {
    return {
      delegation: this.delegationSystem?.getDelegationAnalytics(),
      consensus: this.consensusEngine?.getConsensusMetrics(),
      handoffs: this.handoffManager?.getHandoffMetrics(),
      patterns: this.patternRegistry?.getPatternAnalytics()
    }
  }

  /**
   * Discovery and load balancing methods
   */

  /**
   * Get agent discovery service
   */
  getDiscoveryService(): AgentDiscoveryService | undefined {
    return this.discoveryService
  }

  /**
   * Get load balancer
   */
  getLoadBalancer(): LoadBalancer | undefined {
    return this.loadBalancer
  }

  /**
   * Get failover manager
   */
  getFailoverManager(): FailoverManager | undefined {
    return this.failoverManager
  }

  /**
   * Discover agents with query
   */
  async discoverAgents(query: any = {}): Promise<any> {
    if (!this.discoveryService) {
      throw new Error('Agent discovery service not enabled')
    }
    return await this.discoveryService.discoverAgents(query)
  }

  /**
   * Find best agent for task
   */
  async findBestAgent(query: any): Promise<any> {
    if (!this.discoveryService) {
      throw new Error('Agent discovery service not enabled')
    }
    return await this.discoveryService.findBestAgent(query)
  }

  /**
   * Route request to optimal agent
   */
  async routeRequest(request: any): Promise<any> {
    if (!this.loadBalancer) {
      throw new Error('Load balancer not enabled')
    }
    return await this.loadBalancer.routeRequest(request)
  }

  /**
   * Create failover plan for agent
   */
  async createFailoverPlan(agentId: string): Promise<any> {
    if (!this.failoverManager) {
      throw new Error('Failover manager not enabled')
    }
    return await this.failoverManager.createFailoverPlan(agentId)
  }

  /**
   * Get discovery and load balancing analytics
   */
  getDiscoveryAnalytics(): any {
    return {
      discovery: this.discoveryService?.getDiscoveryStats(),
      loadBalancing: this.loadBalancer?.getMetrics(),
      failover: this.failoverManager?.getFailoverMetrics(),
      loadDistribution: this.loadBalancer?.getLoadDistribution()
    }
  }

  /**
   * Shutdown framework and all components
   */
  async shutdown(): Promise<void> {
    console.log('🔄 Shutting down AG3NT Framework...')

    // Shutdown advanced features
    if (this.advancedFeatures) {
      await this.advancedFeatures.shutdown()
    }

    // Shutdown context engine
    if (this.contextEngine) {
      await this.contextEngine.shutdown()
    }

    // Shutdown other components
    await this.workflowCoordinator.shutdown()
    await this.communication.shutdown()
    await this.registry.shutdown()

    this.isInitialized = false
    this.removeAllListeners()
    console.log('✅ AG3NT Framework shutdown complete')
  }
}

// Export singleton instance for easy access
export const ag3ntFramework = new AG3NTFramework()

// Export main class
export default AG3NTFramework
