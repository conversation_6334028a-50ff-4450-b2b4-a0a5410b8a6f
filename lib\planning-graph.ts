// import { StateGraph, END } from "@langchain/langgraph"
import { ChatOpenAI } from "@langchain/openai"
import { HumanMessage, SystemMessage } from "@langchain/core/messages"
import { UnifiedContextEngine } from "./unified-context-engine-v2"

interface PlanningState {
  prompt: string
  isInteractive: boolean
  userAnswers: Record<string, string>
  currentStep: string
  results: Record<string, any>
  needsInput?: boolean
  question?: {
    id: string
    question: string
    type: string
    placeholder?: string
    optional?: boolean
  }
  completed: boolean
}

export class PlanningGraph {
  private model: ChatOpenAI | null = null
  private contextEngine: UnifiedContextEngine | null = null

  constructor() {
    // Model will be initialized lazily when first needed
  }

  /**
   * Initialize context engine for API usage
   */
  async initializeContextEngine(state: PlanningState): Promise<void> {
    if (!this.contextEngine) {
      this.contextEngine = new ContextEngine({
        originalPrompt: state.prompt || "",
        projectType: "",
        features: [],
        clarifications: {},
        summary: "",
        techStack: {},
        prd: {},
        wireframes: [],
        filesystem: {},
        workflow: {},
        tasks: [],
        ...state.results
      })
    }
  }

  private ensureModel(): void {
    if (!this.model) {
      const apiKey = process.env.OPENROUTER_API_KEY;
      if (!apiKey) {
        throw new Error('OPENROUTER_API_KEY environment variable is required');
      }

      // Initialize OpenRouter Claude 3.5
      this.model = new ChatOpenAI({
        modelName: "anthropic/claude-3.5-sonnet",
        openAIApiKey: apiKey,
        configuration: {
          baseURL: "https://openrouter.ai/api/v1",
          defaultHeaders: {
            "HTTP-Referer": process.env.NEXT_PUBLIC_SITE_URL || "http://localhost:3000",
            "X-Title": "AG3NT.png",
          },
        },
        temperature: 0.7,
      })
    }
  }



  async execute(initialState: Partial<PlanningState>): Promise<any> {
    this.ensureModel(); // Ensure model is initialized before execution

    // Initialize unified context engine for all ADE agents
    this.contextEngine = await createUnifiedEngine({
      originalPrompt: initialState.prompt || "",
      projectType: "",
      features: [],
      clarifications: {},
      summary: "",
      techStack: {},
      prd: {},
      wireframes: [],
      filesystem: {},
      workflow: {},
      tasks: []
    })

    // Register this planning agent with the context engine
    const plannerAgentId = this.contextEngine.registerAgent('project-planner', 'main-planning', {
      requiredCapabilities: ['requirements_analysis', 'tech_stack_selection', 'architecture_design', 'cross_validation'],
      contextFilters: ['all'] // Project planner needs full context
    })

    const state: PlanningState = {
      prompt: initialState.prompt || "",
      isInteractive: initialState.isInteractive || false,
      userAnswers: initialState.userAnswers || {},
      currentStep: initialState.currentStep || "analyze",
      // Always start with a fresh results object
      results: {
        wireframes: undefined,
        prd: undefined,
        summary: undefined,
        techstack: undefined,
        analyze: undefined,
        clarify: undefined,
        filesystem: undefined,
        workflow: undefined,
        tasks: undefined,
        "context-profile": undefined,
      },
      completed: false,
    }

    try {
      // Execute steps sequentially with enhanced context propagation
      let currentState = state
      let steps = ["analyze"] // Always start with analyze

      // Execute analyze step first to determine project type
      currentState = await this.executeStepWithContext("analyze", currentState)
      if (currentState.needsInput) {
        return currentState
      }

      // Get dynamic step sequence based on project type
      const projectType = currentState.results.analyze?.projectType
      steps = this.getStepsForProjectType(projectType)

      // Execute remaining steps with full context propagation
      for (const step of steps.slice(1)) {
        currentState = await this.executeStepWithContext(step, currentState)
        if (currentState.needsInput) {
          break // Stop execution if input is needed
        }
      }

      // Run cross-reference validation for bulletproof planning
      const crossRefValidation = await this.contextEngine.validateCrossReferences()

      // Export final context with integrity report
      const finalContext = this.contextEngine.exportContext()
      currentState.contextIntegrity = finalContext.validation
      currentState.contextMetadata = finalContext.metadata
      currentState.crossReferenceValidation = crossRefValidation

      // Update shared state for downstream agents
      this.contextEngine.updateSharedState('project_status', 'planning_complete', plannerAgentId)
      this.contextEngine.updateSharedState('planning_output', currentState, plannerAgentId)

      // Unregister the planning agent
      this.contextEngine.unregisterAgent(plannerAgentId)

      return currentState
    } catch (error) {
      console.error("Graph execution error:", error)
      throw error
    }
  }

  /**
   * Execute step with enhanced context propagation and MCP integration
   */
  async executeStepWithContext(step: string, state: PlanningState): Promise<PlanningState> {
    if (!this.contextEngine) {
      throw new Error("Context engine not initialized")
    }

    console.log(`🧠 Executing ${step} with MCP-enhanced context...`)

    // 1. Get enhanced context with MCP enrichment
    const enhancedContext = await this.contextEngine.enhanceWithRAG(step, state.prompt)
    console.log(`📚 Enhanced context includes ${enhancedContext.enrichments?.length || 0} enrichments`)

    // 2. Use sequential thinking for complex steps
    let sequentialThought = null
    if (['analyze', 'techstack', 'prd', 'workflow'].includes(step)) {
      sequentialThought = await this.contextEngine.performSequentialThinking(
        `Planning ${step} step for: ${state.prompt}`,
        { thoughtNumber: 1, totalThoughts: 3 }
      )
      console.log(`🤔 Sequential thinking applied for ${step}`)
    }

    // 3. Get relevant documentation if tech stack is involved
    let relevantDocs = null
    if (['techstack', 'prd', 'filesystem', 'workflow'].includes(step) && state.results?.techstack) {
      const techStack = state.results.techstack
      relevantDocs = {}

      if (techStack.frontend) {
        const frontendTech = typeof techStack.frontend === 'string'
          ? techStack.frontend
          : techStack.frontend.framework || techStack.frontend.name || 'React'
        relevantDocs.frontend = await this.contextEngine.getDocumentation(
          frontendTech,
          `${step} best practices`
        )
      }

      if (techStack.backend) {
        const backendTech = typeof techStack.backend === 'string'
          ? techStack.backend
          : techStack.backend.framework || techStack.backend.name || 'Node.js'
        relevantDocs.backend = await this.contextEngine.getDocumentation(
          backendTech,
          `${step} implementation`
        )
      }

      console.log(`📖 Retrieved documentation for ${Object.keys(relevantDocs).length} technologies`)
    }

    // 4. Execute the step with MCP-enhanced context
    const mcpEnhancedState = {
      ...state,
      mcpContext: {
        enhancedContext,
        sequentialThought,
        relevantDocs,
        step
      }
    }

    const result = await this.executeStepWithMCP(step, mcpEnhancedState)

    // 5. Update context engine with results
    if (result.results && result.results[step]) {
      this.contextEngine.updateContext(step, result.results[step])
    }

    console.log(`✅ ${step} completed with MCP enhancement`)
    return result
  }

  /**
   * Execute step with MCP-enhanced context
   */
  private async executeStepWithMCP(step: string, state: any): Promise<PlanningState> {
    // Execute the specific step with MCP context
    switch (step) {
      case "analyze":
        return await this.analyzePromptWithMCP(state)
      case "clarify":
        return await this.clarifyRequirementsWithMCP(state)
      case "summary":
        return await this.generateSummaryWithMCP(state)
      case "techstack":
        return await this.selectTechStackWithMCP(state)
      case "prd":
        return await this.createPRDWithMCP(state)
      case "context-profile":
        return await this.generateContextProfile(state)
      case "wireframes":
        return await this.designWireframesWithMCP(state)
      case "design":
        return await this.createDesignGuidelines(state)
      case "database":
        return await this.designDatabaseSchemaWithMCP(state)
      case "filesystem":
        return await this.planFilesystemWithMCP(state)
      case "workflow":
        return await this.defineWorkflowWithMCP(state)
      case "tasks":
        return await this.breakdownTasksWithMCP(state)
      case "scaffold":
        return await this.generateScaffoldWithMCP(state)
      default:
        // Fallback to original method for steps not yet enhanced
        return await this.executeStep(step, state)
    }
  }

  /**
   * Get step sequence based on project type
   */
  private getStepsForProjectType(projectType: string): string[] {
    const baseSteps = ["analyze", "clarify", "summary", "techstack", "prd"]

    // Add optional steps based on project type
    if (this.isAIAgentProject(projectType)) {
      baseSteps.push("context-profile")
    }

    // Add database step for projects that need databases
    if (this.needsDatabaseStep(projectType)) {
      baseSteps.push("database")
    }

    // Continue with common steps
    baseSteps.push("wireframes", "design", "filesystem", "workflow", "tasks")

    return baseSteps
  }

  /**
   * Check if project is an AI agent that needs context profiles
   */
  private isAIAgentProject(projectType: string): boolean {
    if (!projectType) return false
    const lowerType = projectType.toLowerCase()
    return lowerType.includes("agent") ||
           lowerType.includes("bot") ||
           lowerType.includes("ai") ||
           lowerType === "ai agent"
  }

  /**
   * Check if project needs database design step
   */
  private needsDatabaseStep(projectType: string): boolean {
    if (!projectType) return false
    const lowerType = projectType.toLowerCase()
    return lowerType.includes("web") ||
           lowerType.includes("app") ||
           lowerType.includes("api") ||
           lowerType.includes("system")
  }

  async executeStep(step: string, context: PlanningState, answer?: string): Promise<any> {
    // Update context with new answer if provided
    if (answer && context.question) {
      context.userAnswers[context.question.id] = answer
    }

    // Execute the specific step
    switch (step) {
      case "analyze":
        return await this.analyzePrompt(context)
      case "clarify":
        return await this.clarifyRequirements(context)
      case "summary":
        return await this.generateSummary(context)
      case "techstack":
        return await this.selectTechStack(context)
      case "prd":
        return await this.createPRD(context)
      case "context-profile":
        return await this.generateContextProfile(context)
      case "wireframes":
        return await this.designWireframes(context)
      case "design":
        return await this.createDesignGuidelines(context)
      case "database":
        return await this.designDatabaseSchema(context)
      case "filesystem":
        return await this.planFilesystem(context)
      case "workflow":
        return await this.defineWorkflow(context)
      case "tasks":
        return await this.breakdownTasks(context)
      default:
        throw new Error(`Unknown step: ${step}`)
    }
  }

  /**
   * MCP-Enhanced Analysis with Sequential Thinking and Real-time Context
   */
  private async analyzePromptWithMCP(state: any): Promise<PlanningState> {
    this.ensureModel();

    const mcpContext = state.mcpContext
    const sequentialThought = mcpContext?.sequentialThought
    const enrichments = mcpContext?.enhancedContext?.enrichments || []

    // Build enhanced system prompt with MCP context
    let systemPrompt = `You are a senior project analyst with access to real-time industry knowledge and advanced reasoning capabilities.

    SEQUENTIAL THINKING CONTEXT:
    ${sequentialThought ? `Previous reasoning: ${JSON.stringify(sequentialThought, null, 2)}` : 'No sequential thinking context available'}

    INDUSTRY ENRICHMENTS:
    ${enrichments.length > 0 ? enrichments.map(e => `- ${e.title}: ${e.description} (${e.impact} impact)`).join('\n') : 'No specific industry enrichments available'}

    Analyze this project prompt with enhanced context and provide a structured analysis including:
    1. Project type (Web Application, Mobile App, AI Agent, etc.)
    2. Key features identified
    3. Complexity assessment (Simple, Medium, Complex)
    4. Domain/industry
    5. Technical requirements hints
    6. Risk factors based on current industry standards
    7. Success criteria
    8. Estimated timeframe and team size

    Consider the enrichments and reasoning context above in your analysis.
    Respond in JSON format with these exact keys: projectType, features, complexity, domain, technicalHints, riskFactors, successCriteria, estimatedTimeframe, teamSize`

    const response = await this.model!.invoke([
      new SystemMessage(systemPrompt),
      new HumanMessage(`Project prompt: "${state.prompt}"`),
    ])

    try {
      const analysis = JSON.parse(response.content as string)

      // Add MCP metadata
      analysis._mcpEnhanced = true
      analysis._enrichmentCount = enrichments.length
      analysis._hasSequentialThinking = !!sequentialThought

      return {
        ...state,
        results: {
          ...state.results,
          analyze: analysis,
        },
      }
    } catch (error) {
      // Fallback if JSON parsing fails
      return {
        ...state,
        results: {
          ...state.results,
          analyze: {
            projectType: "Web Application",
            features: ["Core Functionality"],
            complexity: "Medium",
            domain: "General",
            technicalHints: "Modern web technologies recommended",
          },
        },
      }
    }
  }

  private async clarifyRequirements(state: PlanningState): Promise<PlanningState> {
    if (state.isInteractive) {
      // Check if we need to ask questions
      const requiredQuestions = ["target_users", "platform"]
      const missingAnswers = requiredQuestions.filter((q) => !state.userAnswers[q])

      if (missingAnswers.length > 0) {
        const questionMap: Record<string, any> = {
          target_users: {
            id: "target_users",
            question: "Who are the primary users of this application?",
            type: "text",
            placeholder: "e.g., Small business owners, students, developers...",
          },
          platform: {
            id: "platform",
            question: "What platform should this run on?",
            type: "text",
            placeholder: "e.g., Web, mobile, desktop, or all platforms...",
          },
        }

        return {
          ...state,
          needsInput: true,
          question: questionMap[missingAnswers[0]],
        }
      }
    }

    // Generate clarification results
    this.ensureModel();
    const systemPrompt = `Based on the project analysis and user answers, provide detailed clarification of requirements.

    Project: ${state.prompt}
    Analysis: ${JSON.stringify(state.results.analyze)}
    User Answers: ${JSON.stringify(state.userAnswers)}

    Provide clarification in JSON format with keys: targetUsers, platform, requirements, scope`

    const response = await this.model!.invoke([
      new SystemMessage(systemPrompt),
      new HumanMessage("Generate detailed requirements clarification."),
    ])

    try {
      const clarification = JSON.parse(response.content as string)

      return {
        ...state,
        results: {
          ...state.results,
          clarify: clarification,
        },
        needsInput: false,
        question: undefined,
      }
    } catch (error) {
      return {
        ...state,
        results: {
          ...state.results,
          clarify: {
            targetUsers: state.userAnswers.target_users || "General users",
            platform: state.userAnswers.platform || "Web application",
            requirements: "Requirements clarified based on user input",
            scope: "Project scope defined",
          },
        },
        needsInput: false,
        question: undefined,
      }
    }
  }

  private async generateSummary(state: PlanningState): Promise<PlanningState> {
    this.ensureModel();
    const systemPrompt = `Create a comprehensive project summary based on the analysis and clarification.

    Project: ${state.prompt}
    Analysis: ${JSON.stringify(state.results.analyze)}
    Clarification: ${JSON.stringify(state.results.clarify)}

    Generate a project summary in JSON format with keys: overview, scope, goals, keyFeatures`

    const response = await this.model!.invoke([
      new SystemMessage(systemPrompt),
      new HumanMessage("Generate a comprehensive project summary."),
    ])

    try {
      const summary = JSON.parse(response.content as string)

      return {
        ...state,
        results: {
          ...state.results,
          summary: summary,
        },
      }
    } catch (error) {
      return {
        ...state,
        results: {
          ...state.results,
          summary: {
            overview: `A ${state.results.analyze?.projectType || "application"} that ${state.prompt}`,
            scope: `Targeting ${state.results.clarify?.targetUsers || "users"} on ${state.results.clarify?.platform || "web platform"}`,
            goals: state.results.analyze?.features || ["Core functionality"],
            keyFeatures: state.results.analyze?.features || ["Primary features"],
          },
        },
      }
    }
  }

  private async selectTechStack(state: PlanningState): Promise<PlanningState> {
    if (state.isInteractive && !state.userAnswers.preferences) {
      return {
        ...state,
        needsInput: true,
        question: {
          id: "preferences",
          question: "Any technology preferences?",
          type: "text",
          placeholder: "e.g., React, Python, specific databases...",
          optional: true,
        },
      }
    }

    this.ensureModel();
    const systemPrompt = `Recommend a technology stack based on the project requirements.

    Project: ${state.prompt}
    Analysis: ${JSON.stringify(state.results.analyze)}
    Clarification: ${JSON.stringify(state.results.clarify)}
    User Preferences: ${state.userAnswers.preferences || "None specified"}

    Recommend technologies in JSON format with keys: frontend, backend, database, hosting, authentication, preferences`

    const response = await this.model!.invoke([
      new SystemMessage(systemPrompt),
      new HumanMessage("Recommend an appropriate technology stack."),
    ])

    try {
      const techStack = JSON.parse(response.content as string)

      return {
        ...state,
        results: {
          ...state.results,
          techstack: techStack,
        },
        needsInput: false,
        question: undefined,
      }
    } catch (error) {
      return {
        ...state,
        results: {
          ...state.results,
          techstack: {
            frontend: "React",
            backend: "Node.js",
            database: "PostgreSQL",
            hosting: "Vercel",
            authentication: "NextAuth.js",
            preferences: state.userAnswers.preferences || "Modern, scalable stack",
          },
        },
        needsInput: false,
        question: undefined,
      }
    }
  }

  /**
   * MCP-Enhanced Tech Stack Selection with Real-time Compatibility Checking
   */
  private async selectTechStackWithMCP(state: any): Promise<PlanningState> {
    if (state.isInteractive && !state.userAnswers.preferences) {
      return {
        ...state,
        needsInput: true,
        question: {
          id: "preferences",
          question: "Any technology preferences?",
          type: "text",
          placeholder: "e.g., React, Python, specific databases...",
          optional: true,
        },
      }
    }

    this.ensureModel();

    const mcpContext = state.mcpContext
    const sequentialThought = mcpContext?.sequentialThought
    const enrichments = mcpContext?.enhancedContext?.enrichments || []

    // Get real-time tech compatibility data if available
    let compatibilityData = null
    if (this.contextEngine) {
      try {
        // Search for current tech stack best practices
        const techBestPractices = await this.contextEngine.searchDocumentation(
          `${state.results.analyze?.projectType} technology stack best practices 2024`
        )
        compatibilityData = techBestPractices
      } catch (error) {
        console.warn('Could not fetch real-time tech data:', error)
      }
    }

    const systemPrompt = `You are a senior technical architect with expertise in modern technology stacks and access to real-time industry data. Recommend the optimal technology stack.

    🧠 SEQUENTIAL THINKING CONTEXT:
    ${sequentialThought ? `Previous reasoning: ${JSON.stringify(sequentialThought, null, 2)}` : 'No sequential thinking context available'}

    📚 INDUSTRY ENRICHMENTS (${enrichments.length} available):
    ${enrichments.length > 0 ? enrichments.map(e => `- ${e.title}: ${e.description} (${e.impact} impact)`).join('\n') : 'No specific industry enrichments available'}

    🔍 REAL-TIME COMPATIBILITY DATA:
    ${compatibilityData ? JSON.stringify(compatibilityData, null, 2) : 'No real-time compatibility data available'}

    Return a JSON object with this EXACT structure:

    {
      "frontend": {
        "framework": "React" | "Vue" | "Angular" | "Svelte" | "Next.js" | "other",
        "language": "TypeScript" | "JavaScript",
        "styling": "Tailwind CSS" | "Styled Components" | "CSS Modules" | "other",
        "reasoning": "Why this choice based on MCP context and enrichments"
      },
      "backend": {
        "framework": "Node.js" | "Python" | "Go" | "Rust" | "Java" | "C#" | "other",
        "database": "PostgreSQL" | "MongoDB" | "MySQL" | "SQLite" | "other",
        "authentication": "Auth0" | "Firebase Auth" | "NextAuth" | "custom" | "other",
        "reasoning": "Why this choice based on real-time data"
      },
      "infrastructure": {
        "hosting": "Vercel" | "Netlify" | "AWS" | "Google Cloud" | "Azure" | "other",
        "cicd": "GitHub Actions" | "GitLab CI" | "Jenkins" | "other",
        "monitoring": "Sentry" | "LogRocket" | "DataDog" | "other",
        "reasoning": "Why this choice considering enrichments"
      },
      "additionalTools": ["tool1", "tool2"],
      "alternatives": {
        "considered": ["alternative1", "alternative2"],
        "reasoning": "Why alternatives were not chosen based on MCP analysis"
      },
      "mcpEnhanced": {
        "enrichmentsUsed": ${enrichments.length},
        "sequentialThinkingApplied": ${!!sequentialThought},
        "compatibilityDataAvailable": ${!!compatibilityData}
      }
    }

    Consider project complexity, team size, timeline, specific requirements, AND the MCP context above.
    CRITICAL: Return ONLY the JSON object. Do not include any explanatory text.`

    const response = await this.model!.invoke([
      new SystemMessage(systemPrompt),
      new HumanMessage("Recommend an appropriate technology stack with enhanced context."),
    ])

    try {
      const techStack = JSON.parse(response.content as string)

      // Add MCP metadata
      techStack._mcpEnhanced = true
      techStack._enrichmentCount = enrichments.length
      techStack._hasSequentialThinking = !!sequentialThought
      techStack._hasCompatibilityData = !!compatibilityData

      return {
        ...state,
        results: {
          ...state.results,
          techstack: techStack,
        },
        needsInput: false,
        question: undefined,
      }
    } catch (error) {
      // Enhanced fallback with MCP context
      return {
        ...state,
        results: {
          ...state.results,
          techstack: {
            frontend: "React",
            backend: "Node.js",
            database: "PostgreSQL",
            hosting: "Vercel",
            authentication: "NextAuth.js",
            preferences: state.userAnswers.preferences || "Modern, scalable stack",
            _mcpEnhanced: true,
            _fallback: true,
            reasoning: "Fallback to proven modern stack due to parsing error"
          },
        },
        needsInput: false,
        question: undefined,
      }
    }
  }

  private async createPRD(state: PlanningState): Promise<PlanningState> {
    if (state.isInteractive && !state.userAnswers.timeline) {
      return {
        ...state,
        needsInput: true,
        question: {
          id: "timeline",
          question: "What's your target timeline?",
          type: "text",
          placeholder: "e.g., 2 weeks, 1 month, 3 months...",
          optional: true,
        },
      }
    }

    this.ensureModel();
    const systemPrompt = `Create a Product Requirements Document based on all gathered information.

    Project: ${state.prompt}
    Analysis: ${JSON.stringify(state.results.analyze)}
    Clarification: ${JSON.stringify(state.results.clarify)}
    Summary: ${JSON.stringify(state.results.summary)}
    Tech Stack: ${JSON.stringify(state.results.techstack)}
    Timeline: ${state.userAnswers.timeline || "Not specified"}

    Generate PRD in JSON format with keys: timeline, features, userStories, requirements, acceptanceCriteria`

    const response = await this.model!.invoke([
      new SystemMessage(systemPrompt),
      new HumanMessage("Create a comprehensive Product Requirements Document."),
    ])

    try {
      const prd = JSON.parse(response.content as string)

      return {
        ...state,
        results: {
          ...state.results,
          prd: prd,
        },
        needsInput: false,
        question: undefined,
      }
    } catch (error) {
      return {
        ...state,
        results: {
          ...state.results,
          prd: {
            timeline: state.userAnswers.timeline || "8-12 weeks",
            features: state.results.analyze?.features || ["Core functionality"],
            userStories: [`As a user, I want to ${state.prompt.toLowerCase()}`],
            requirements: "Comprehensive requirements documented",
            acceptanceCriteria: "Acceptance criteria defined",
          },
        },
        needsInput: false,
        question: undefined,
      }
    }
  }

  private async generateContextProfile(state: PlanningState): Promise<PlanningState> {
    this.ensureModel();
    const systemPrompt = `Create a comprehensive Structured JSON Context Profile Template for an AI agent.

    Project: ${state.prompt}
    Analysis: ${JSON.stringify(state.results.analyze)}
    Summary: ${JSON.stringify(state.results.summary)}
    PRD: ${JSON.stringify(state.results.prd)}

    Generate a detailed context profile template that defines the AI agent's identity, capabilities, goals, and operational parameters.

    Return a JSON object with this exact structure:
    {
      "profile_id": "agent-[domain]-[role]-v1.0.0",
      "identity": {
        "name": "[Agent Name]",
        "role": "[Specific Role]",
        "organization": "[Organization/Domain]",
        "timezone": "UTC",
        "language": "en-US"
      },
      "goals": {
        "short_term": ["goal1", "goal2"],
        "long_term": ["goal1", "goal2"]
      },
      "preferences": {
        "communication_style": "[style]",
        "response_format": "[format]",
        "tone": "[tone]",
        "visuals": true/false,
        "default_output_type": "[type]"
      },
      "capabilities": {
        "tools_enabled": ["tool1", "tool2"],
        "environment": {
          "platform": "[platform]",
          "extensions": ["ext1", "ext2"]
        }
      },
      "memory": {
        "scope": "[scope]",
        "persistence": "[type]",
        "structure": "[structure]",
        "data_points": ["point1", "point2"]
      },
      "constraints": {
        "rate_limit": "[limit]",
        "budget": {
          "monthly": 0,
          "used": 0
        },
        "operational_constraints": ["constraint1", "constraint2"]
      },
      "behavioral_flags": {
        "debug_mode": false,
        "auto_summarize": true/false,
        "use_context_window": true/false
      },
      "metadata": {
        "created_at": "[ISO timestamp]",
        "last_updated": "[ISO timestamp]",
        "version": "1.0.0"
      }
    }`

    const response = await this.model!.invoke([
      new SystemMessage(systemPrompt),
      new HumanMessage("Generate the context profile template for this AI agent."),
    ])

    try {
      const contextProfile = JSON.parse(response.content as string)

      return {
        ...state,
        results: {
          ...state.results,
          "context-profile": contextProfile,
        },
        needsInput: false,
        question: undefined,
      }
    } catch (error) {
      // Fallback context profile template
      const fallbackProfile = {
        profile_id: "agent-assistant-v1.0.0",
        identity: {
          name: "AI Assistant",
          role: "General Purpose Assistant",
          organization: "AI Services",
          timezone: "UTC",
          language: "en-US"
        },
        goals: {
          short_term: ["Assist users with tasks", "Provide accurate information"],
          long_term: ["Improve user productivity", "Learn from interactions"]
        },
        preferences: {
          communication_style: "helpful_and_clear",
          response_format: "structured_text",
          tone: "professional_friendly",
          visuals: false,
          default_output_type: "text_response"
        },
        capabilities: {
          tools_enabled: ["text_processing", "information_retrieval"],
          environment: {
            platform: "Cloud-based",
            extensions: ["web_search", "document_analysis"]
          }
        },
        memory: {
          scope: "conversation",
          persistence: "session",
          structure: "contextual",
          data_points: ["user_preferences", "conversation_history"]
        },
        constraints: {
          rate_limit: "1000 requests/hour",
          budget: {
            monthly: 100,
            used: 0
          },
          operational_constraints: ["respect_privacy", "provide_accurate_info"]
        },
        behavioral_flags: {
          debug_mode: false,
          auto_summarize: true,
          use_context_window: true
        },
        metadata: {
          created_at: new Date().toISOString(),
          last_updated: new Date().toISOString(),
          version: "1.0.0"
        }
      }

      return {
        ...state,
        results: {
          ...state.results,
          "context-profile": fallbackProfile,
        },
        needsInput: false,
        question: undefined,
      }
    }
  }

  private async designWireframes(state: PlanningState): Promise<PlanningState> {
    this.ensureModel();
    const systemPrompt = `Design wireframes based on the project requirements.

    Project: ${state.prompt}
    Summary: ${JSON.stringify(state.results.summary)}
    PRD: ${JSON.stringify(state.results.prd)}

    Generate wireframe plan in JSON format with keys: pages, components, responsive, navigation`

    const response = await this.model!.invoke([
      new SystemMessage(systemPrompt),
      new HumanMessage("Design wireframes for the application."),
    ])

    try {
      const wireframes = JSON.parse(response.content as string)

      return {
        ...state,
        results: {
          ...state.results,
          wireframes: wireframes,
        },
      }
    } catch (error) {
      return {
        ...state,
        results: {
          ...state.results,
          wireframes: {
            pages: [
              {
                name: "Landing Page",
                type: "landing",
                purpose: "Welcome users and showcase agent builder capabilities",
                wireframe: "┌─────────────────────────────────────────────────────────┐\n│                    AGENT BUILDER                       │\n├─────────────────────────────────────────────────────────┤\n│ [Logo]              Navigation              [Sign In]   │\n├─────────────────────────────────────────────────────────┤\n│                                                         │\n│              Build Any LangGraph Agent                  │\n│                   Instantly with E2B                   │\n│                                                         │\n│              ┌─────────────────────┐                   │\n│              │   Get Started Now   │                   │\n│              └─────────────────────┘                   │\n│                                                         │\n│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐      │\n│  │  Templates  │ │ Monitoring  │ │ Deployment  │      │\n│  │    Fast     │ │  Real-time  │ │   Instant   │      │\n│  └─────────────┘ └─────────────┘ └─────────────┘      │\n└─────────────────────────────────────────────────────────┘",
                components: ["Header", "Hero Section", "Feature Cards", "CTA Button"],
                interactions: ["Sign In", "Get Started", "View Templates"]
              },
              {
                name: "Agent Dashboard",
                type: "dashboard",
                purpose: "Manage and monitor deployed agents",
                wireframe: "┌─────────────────────────────────────────────────────────┐\n│ [Logo] Dashboard  Templates  Monitor    [User Profile]  │\n├─────────────────────────────────────────────────────────┤\n│                                                         │\n│  My Agents                           ┌─────────────┐    │\n│                                      │ Create New  │    │\n│  ┌─────────────────────────────────┐ │   Agent     │    │\n│  │ Agent Name: ChatBot v1          │ └─────────────┘    │\n│  │ Status: ● Running               │                    │\n│  │ E2B Environment: env_123        │ ┌─────────────┐    │\n│  │ [View] [Stop] [Logs] [Metrics]  │ │   Quick     │    │\n│  └─────────────────────────────────┘ │  Actions    │    │\n│                                      └─────────────┘    │\n│  ┌─────────────────────────────────┐                    │\n│  │ Agent Name: Data Processor      │ Recent Activity:   │\n│  │ Status: ○ Stopped               │ • Agent deployed   │\n│  │ E2B Environment: env_456        │ • Metrics updated  │\n│  │ [View] [Start] [Logs] [Delete]  │ • Log entry added  │\n│  └─────────────────────────────────┘                    │\n└─────────────────────────────────────────────────────────┘",
                components: ["Navigation", "Agent Cards", "Status Indicators", "Action Buttons", "Activity Feed"],
                interactions: ["Create Agent", "Start/Stop Agent", "View Logs", "View Metrics"]
              },
              {
                name: "Agent Builder",
                type: "form",
                purpose: "Configure and deploy new LangGraph agents",
                wireframe: "┌─────────────────────────────────────────────────────────┐\n│ [Logo] Dashboard  Templates  Monitor    [User Profile]  │\n├─────────────────────────────────────────────────────────┤\n│                                                         │\n│  Create New Agent                                       │\n│                                                         │\n│  ┌─────────────────────────────────────────────────┐   │\n│  │ Agent Name: [_________________________]         │   │\n│  │                                                 │   │\n│  │ Template:   [Select Template ▼]                │   │\n│  │                                                 │   │\n│  │ Configuration:                                  │   │\n│  │ ┌─────────────────────────────────────────────┐ │   │\n│  │ │ {                                           │ │   │\n│  │ │   \"model\": \"gpt-4\",                         │ │   │\n│  │ │   \"temperature\": 0.7,                       │ │   │\n│  │ │   \"max_tokens\": 1000                        │ │   │\n│  │ │ }                                           │ │   │\n│  │ └─────────────────────────────────────────────┘ │   │\n│  │                                                 │   │\n│  │ [Cancel]                        [Deploy Agent] │   │\n│  └─────────────────────────────────────────────────┘   │\n└─────────────────────────────────────────────────────────┘",
                components: ["Form Fields", "Template Selector", "JSON Editor", "Deploy Button"],
                interactions: ["Select Template", "Edit Configuration", "Deploy Agent", "Cancel"]
              }
            ],
            components: [
              {
                name: "Agent Card",
                type: "card",
                description: "Displays agent information and controls",
                props: ["agentName", "status", "environmentId", "actions"]
              },
              {
                name: "Status Indicator",
                type: "other",
                description: "Shows running/stopped status with color coding",
                props: ["status", "color"]
              }
            ],
            userFlow: [
              {
                step: 1,
                action: "User visits landing page",
                page: "Landing Page",
                result: "Sees agent builder overview and features"
              },
              {
                step: 2,
                action: "User clicks 'Get Started'",
                page: "Agent Dashboard",
                result: "Views their agent dashboard"
              },
              {
                step: 3,
                action: "User clicks 'Create New Agent'",
                page: "Agent Builder",
                result: "Configures and deploys new agent"
              }
            ],
            responsive: {
              breakpoints: ["mobile", "tablet", "desktop"],
              considerations: ["Mobile-first design", "Touch-friendly controls", "Responsive grid layout"]
            },
            _fallback: true
          },
        },
      }
    }
  }

  private async createDesignGuidelines(state: PlanningState): Promise<PlanningState> {
    this.ensureModel();
    const systemPrompt = `Create design guidelines based on the project requirements and wireframes.

    Project: ${state.prompt}
    Tech Stack: ${JSON.stringify(state.results.techstack)}
    Wireframes: ${JSON.stringify(state.results.wireframes)}

    Generate design guidelines in JSON format with keys: theme, colorPalette, typography, layout, interactive, effects, animations`

    const response = await this.model!.invoke([
      new SystemMessage(systemPrompt),
      new HumanMessage("Create comprehensive design guidelines for the application."),
    ])

    try {
      const design = JSON.parse(response.content as string)

      return {
        ...state,
        results: {
          ...state.results,
          design: design,
        },
      }
    } catch (error) {
      return {
        ...state,
        results: {
          ...state.results,
          design: {
            theme: "Modern and clean design",
            colorPalette: {
              primary: "#007bff",
              secondary: "#6c757d",
              success: "#28a745",
              danger: "#dc3545",
            },
            typography: "Clean, readable fonts with proper hierarchy",
            layout: "Responsive grid-based layout",
            interactive: "Smooth hover effects and transitions",
            effects: "Subtle shadows and gradients",
            animations: "Smooth page transitions",
          },
        },
      }
    }
  }

  private async planFilesystem(state: PlanningState): Promise<PlanningState> {
    this.ensureModel();
    const systemPrompt = `Plan the file system structure based on the technology stack.

    Tech Stack: ${JSON.stringify(state.results.techstack)}
    Project Type: ${state.results.analyze?.projectType}

    Generate filesystem plan in JSON format with keys: structure, folders, files, organization`

    const response = await this.model!.invoke([
      new SystemMessage(systemPrompt),
      new HumanMessage("Plan the project file system structure."),
    ])

    try {
      const filesystem = JSON.parse(response.content as string)

      return {
        ...state,
        results: {
          ...state.results,
          filesystem: filesystem,
        },
      }
    } catch (error) {
      return {
        ...state,
        results: {
          ...state.results,
          filesystem: {
            structure: "Modern project structure",
            folders: ["src/", "components/", "pages/", "utils/", "styles/"],
            files: "Key files identified and organized",
            organization: "Follows best practices for the selected tech stack",
          },
        },
      }
    }
  }

  private async defineWorkflow(state: PlanningState): Promise<PlanningState> {
    this.ensureModel();
    const systemPrompt = `Define the workflow logic for the application.

    Project: ${state.prompt}
    Project Type: ${state.results.analyze?.projectType}
    Features: ${JSON.stringify(state.results.summary?.keyFeatures)}

    Generate workflow definition in JSON format with keys: steps, logic, integrations, dataFlow`

    const response = await this.model!.invoke([
      new SystemMessage(systemPrompt),
      new HumanMessage("Define the application workflow logic."),
    ])

    try {
      const workflow = JSON.parse(response.content as string)

      return {
        ...state,
        results: {
          ...state.results,
          workflow: workflow,
        },
      }
    } catch (error) {
      return {
        ...state,
        results: {
          ...state.results,
          workflow: {
            steps: ["User Input", "Processing", "Data Storage", "Response"],
            logic: "Workflow logic defined based on project requirements",
            integrations: "API endpoints and external services planned",
            dataFlow: "Data flow patterns established",
          },
        },
      }
    }
  }

  private async breakdownTasks(state: PlanningState): Promise<PlanningState> {
    this.ensureModel();
    const systemPrompt = `Break down the project into implementation tasks.

    Project: ${state.prompt}
    Tech Stack: ${JSON.stringify(state.results.techstack)}
    PRD: ${JSON.stringify(state.results.prd)}
    Timeline: ${state.results.prd?.timeline}

    Generate task breakdown in JSON format with keys: totalTasks, categories, estimate, priority, phases`

    const response = await this.model!.invoke([
      new SystemMessage(systemPrompt),
      new HumanMessage("Break down the project into actionable implementation tasks."),
    ])

    try {
      const tasks = JSON.parse(response.content as string)

      return {
        ...state,
        results: {
          ...state.results,
          tasks: tasks,
        },
        completed: true,
      }
    } catch (error) {
      return {
        ...state,
        results: {
          ...state.results,
          tasks: {
            totalTasks: 24,
            categories: ["Setup", "Frontend", "Backend", "Testing", "Deployment"],
            estimate: state.results.prd?.timeline || "8-12 weeks",
            priority: "High priority tasks identified",
            phases: "Development phases planned",
          },
        },
        completed: true,
      }
    }
  }

  private async designDatabaseSchema(state: PlanningState): Promise<PlanningState> {
    this.ensureModel();
    const systemPrompt = `Design a detailed database schema for the project.

    Project: ${state.prompt}
    Analysis: ${JSON.stringify(state.results.analyze)}
    PRD: ${JSON.stringify(state.results.prd)}
    Tech Stack: ${JSON.stringify(state.results.techstack)}

    Generate database schema in JSON format with this exact structure:
    {
      "databaseType": "relational" | "document" | "graph" | "hybrid",
      "tables": [
        {
          "name": "table_name",
          "purpose": "What this table stores",
          "columns": [
            {
              "name": "column_name",
              "type": "VARCHAR(255)" | "INTEGER" | "BOOLEAN" | "TIMESTAMP" | "TEXT" | "JSON",
              "constraints": ["PRIMARY KEY", "NOT NULL", "UNIQUE", "FOREIGN KEY"],
              "description": "What this column represents"
            }
          ],
          "indexes": [
            {
              "name": "index_name",
              "columns": ["column1", "column2"],
              "type": "btree" | "hash" | "gin" | "gist",
              "purpose": "Why this index is needed"
            }
          ]
        }
      ]
    }`

    const response = await this.model!.invoke([
      new SystemMessage(systemPrompt),
      new HumanMessage("Design the database schema for this application."),
    ])

    try {
      const database = JSON.parse(response.content as string)

      return {
        ...state,
        results: {
          ...state.results,
          database: database,
        },
        needsInput: false,
        question: undefined,
      }
    } catch (error) {
      return {
        ...state,
        results: {
          ...state.results,
          database: {
            databaseType: "relational",
            tables: [
              {
                name: "users",
                purpose: "Store user account information",
                columns: [
                  {
                    name: "id",
                    type: "INTEGER",
                    constraints: ["PRIMARY KEY", "AUTO_INCREMENT"],
                    description: "Unique user identifier"
                  },
                  {
                    name: "email",
                    type: "VARCHAR(255)",
                    constraints: ["NOT NULL", "UNIQUE"],
                    description: "User email address"
                  },
                  {
                    name: "created_at",
                    type: "TIMESTAMP",
                    constraints: ["NOT NULL", "DEFAULT CURRENT_TIMESTAMP"],
                    description: "Account creation timestamp"
                  }
                ],
                indexes: [
                  {
                    name: "idx_users_email",
                    columns: ["email"],
                    type: "btree",
                    purpose: "Fast email lookups for authentication"
                  }
                ]
              },
              {
                name: "sessions",
                purpose: "Store user session data",
                columns: [
                  {
                    name: "id",
                    type: "VARCHAR(255)",
                    constraints: ["PRIMARY KEY"],
                    description: "Session identifier"
                  },
                  {
                    name: "user_id",
                    type: "INTEGER",
                    constraints: ["NOT NULL", "FOREIGN KEY"],
                    description: "Reference to users table"
                  },
                  {
                    name: "expires_at",
                    type: "TIMESTAMP",
                    constraints: ["NOT NULL"],
                    description: "Session expiration time"
                  }
                ]
              }
            ]
          },
        },
        needsInput: false,
        question: undefined,
      }
    }
  }

  /**
   * MCP-Enhanced placeholder methods (to be implemented)
   */
  private async clarifyRequirementsWithMCP(state: any): Promise<PlanningState> {
    console.log('🔍 Using MCP-enhanced clarification...')
    return await this.clarifyRequirements(state)
  }

  private async generateSummaryWithMCP(state: any): Promise<PlanningState> {
    console.log('📝 Using MCP-enhanced summary generation...')
    return await this.generateSummary(state)
  }

  /**
   * MCP-Enhanced PRD Creation with Real-time Industry Standards
   */
  private async createPRDWithMCP(state: any): Promise<PlanningState> {
    this.ensureModel();

    const mcpContext = state.mcpContext
    const sequentialThought = mcpContext?.sequentialThought
    const enrichments = mcpContext?.enhancedContext?.enrichments || []

    // Get real-time PRD best practices
    let prdBestPractices = null
    if (this.contextEngine) {
      try {
        prdBestPractices = await this.contextEngine.searchDocumentation(
          `product requirements document PRD best practices ${state.results.analyze?.projectType}`
        )
      } catch (error) {
        console.warn('Could not fetch PRD best practices:', error)
      }
    }

    const systemPrompt = `You are a senior product manager expert at creating comprehensive PRDs with access to real-time industry standards.

    🧠 SEQUENTIAL THINKING CONTEXT:
    ${sequentialThought ? `Previous reasoning: ${JSON.stringify(sequentialThought, null, 2)}` : 'No sequential thinking context available'}

    📚 INDUSTRY ENRICHMENTS (${enrichments.length} available):
    ${enrichments.length > 0 ? enrichments.map(e => `- ${e.title}: ${e.description} (${e.impact} impact)`).join('\n') : 'No specific industry enrichments available'}

    🔍 REAL-TIME PRD BEST PRACTICES:
    ${prdBestPractices ? JSON.stringify(prdBestPractices, null, 2) : 'No real-time PRD practices available'}

    Create a detailed Product Requirements Document using the MCP context above.

    Return a JSON object with this EXACT structure:

    {
      "overview": "Executive summary of the product",
      "objectives": ["objective1", "objective2"],
      "userStories": [
        {
          "role": "user type",
          "goal": "what they want to do",
          "benefit": "why they want to do it"
        }
      ],
      "functionalRequirements": [
        {
          "id": "FR001",
          "title": "Requirement title",
          "description": "Detailed description",
          "priority": "high" | "medium" | "low",
          "acceptanceCriteria": ["criteria1", "criteria2"]
        }
      ],
      "nonFunctionalRequirements": [
        {
          "id": "NFR001",
          "category": "performance" | "security" | "usability" | "scalability",
          "requirement": "Specific requirement",
          "metric": "How to measure success"
        }
      ],
      "userExperience": {
        "targetUsers": ["user type 1", "user type 2"],
        "userJourney": ["step1", "step2", "step3"],
        "keyInteractions": ["interaction1", "interaction2"]
      },
      "technicalConsiderations": {
        "integrations": ["integration1", "integration2"],
        "dataRequirements": ["requirement1", "requirement2"],
        "securityRequirements": ["security1", "security2"]
      },
      "successMetrics": {
        "kpis": ["kpi1", "kpi2"],
        "targets": ["target1", "target2"]
      },
      "mcpEnhanced": {
        "enrichmentsUsed": ${enrichments.length},
        "sequentialThinkingApplied": ${!!sequentialThought},
        "bestPracticesApplied": ${!!prdBestPractices}
      }
    }

    Consider the analysis, tech stack, AND the MCP context above to create a comprehensive PRD.
    CRITICAL: Return ONLY the JSON object. Do not include any explanatory text.`

    const response = await this.model!.invoke([
      new SystemMessage(systemPrompt),
      new HumanMessage(`Project: ${state.prompt}

Analysis: ${JSON.stringify(state.results.analyze)}
Tech Stack: ${JSON.stringify(state.results.techstack)}
Clarifications: ${JSON.stringify(state.results.clarify)}

Create a detailed PRD for this application using the MCP context.`),
    ])

    try {
      const prd = JSON.parse(response.content as string)

      // Add MCP metadata
      prd._mcpEnhanced = true
      prd._enrichmentCount = enrichments.length
      prd._hasSequentialThinking = !!sequentialThought
      prd._hasBestPractices = !!prdBestPractices

      return {
        ...state,
        results: {
          ...state.results,
          prd: prd,
        },
        needsInput: false,
        question: undefined,
      }
    } catch (error) {
      console.error('MCP PRD parsing failed:', error)
      // Fallback to original method
      return await this.createPRD(state)
    }
  }

  /**
   * MCP-Enhanced Wireframe Design with Real-time UX Best Practices
   */
  private async designWireframesWithMCP(state: any): Promise<PlanningState> {
    this.ensureModel();

    const mcpContext = state.mcpContext
    const sequentialThought = mcpContext?.sequentialThought
    const enrichments = mcpContext?.enhancedContext?.enrichments || []

    // Get real-time UX/wireframe best practices
    let uxBestPractices = null
    if (this.contextEngine) {
      try {
        uxBestPractices = await this.contextEngine.searchDocumentation(
          `wireframe design UX best practices ${state.results.analyze?.projectType}`
        )
      } catch (error) {
        console.warn('Could not fetch UX best practices:', error)
      }
    }

    const systemPrompt = `You are a UX/UI designer expert at creating wireframes with access to real-time design best practices.

    🧠 SEQUENTIAL THINKING CONTEXT:
    ${sequentialThought ? `Previous reasoning: ${JSON.stringify(sequentialThought, null, 2)}` : 'No sequential thinking context available'}

    📚 INDUSTRY ENRICHMENTS (${enrichments.length} available):
    ${enrichments.length > 0 ? enrichments.map(e => `- ${e.title}: ${e.description} (${e.impact} impact)`).join('\n') : 'No specific industry enrichments available'}

    🔍 REAL-TIME UX BEST PRACTICES:
    ${uxBestPractices ? JSON.stringify(uxBestPractices, null, 2) : 'No real-time UX practices available'}

    Design comprehensive wireframes using the MCP context above.

    Return a JSON object with this EXACT structure:

    {
      "pages": [
        {
          "name": "Page name",
          "type": "landing" | "dashboard" | "form" | "list" | "detail" | "other",
          "purpose": "What this page does",
          "wireframe": "ASCII art representation of the layout",
          "components": ["component1", "component2"],
          "interactions": ["interaction1", "interaction2"]
        }
      ],
      "components": [
        {
          "name": "Component name",
          "type": "header" | "navigation" | "form" | "card" | "modal" | "other",
          "description": "What this component does",
          "props": ["prop1", "prop2"]
        }
      ],
      "userFlow": [
        {
          "step": 1,
          "action": "User action",
          "page": "Page name",
          "result": "What happens"
        }
      ],
      "responsive": {
        "breakpoints": ["mobile", "tablet", "desktop"],
        "considerations": ["consideration1", "consideration2"]
      },
      "mcpEnhanced": {
        "enrichmentsUsed": ${enrichments.length},
        "sequentialThinkingApplied": ${!!sequentialThought},
        "uxBestPracticesApplied": ${!!uxBestPractices}
      }
    }

    IMPORTANT:
    - Return only valid JSON
    - Use double quotes for all strings, never backticks
    - For multi-line wireframes, use \\n for line breaks within the string
    - Create detailed ASCII wireframes showing actual layout structure
    - Include boxes, borders, and clear visual hierarchy in ASCII art
    - Consider the MCP context and enrichments in your design decisions

    EXAMPLE WIREFRAME FORMAT:
    "wireframe": "┌─────────────────────────────────────┐\\n│              HEADER                 │\\n├─────────────────────────────────────┤\\n│ [Logo]    Navigation    [Profile]   │\\n├─────────────────────────────────────┤\\n│                                     │\\n│         Main Content Area           │\\n│                                     │\\n│  ┌─────────────┐ ┌─────────────┐   │\\n│  │   Card 1    │ │   Card 2    │   │\\n│  └─────────────┘ └─────────────┘   │\\n│                                     │\\n└─────────────────────────────────────┘"

    CRITICAL: Return ONLY the JSON object. Do not include any explanatory text.`

    const response = await this.model!.invoke([
      new SystemMessage(systemPrompt),
      new HumanMessage(`Project: ${state.prompt}

Analysis: ${JSON.stringify(state.results.analyze)}
PRD: ${JSON.stringify(state.results.prd)}

Design comprehensive wireframes using the MCP context.`),
    ])

    try {
      const wireframes = JSON.parse(response.content as string)

      // Add MCP metadata
      wireframes._mcpEnhanced = true
      wireframes._enrichmentCount = enrichments.length
      wireframes._hasSequentialThinking = !!sequentialThought
      wireframes._hasUXBestPractices = !!uxBestPractices

      return {
        ...state,
        results: {
          ...state.results,
          wireframes: wireframes,
        },
        needsInput: false,
        question: undefined,
      }
    } catch (error) {
      console.error('MCP wireframes parsing failed:', error)
      // Fallback to original method
      return await this.designWireframes(state)
    }
  }

  /**
   * MCP-Enhanced Database Schema Design with Real-time Best Practices
   */
  private async designDatabaseSchemaWithMCP(state: any): Promise<PlanningState> {
    this.ensureModel();

    const mcpContext = state.mcpContext
    const sequentialThought = mcpContext?.sequentialThought
    const enrichments = mcpContext?.enhancedContext?.enrichments || []

    // Get real-time database best practices
    let databaseBestPractices = null
    if (this.contextEngine) {
      try {
        const dbTech = state.results?.techstack?.backend?.database || 'PostgreSQL'
        databaseBestPractices = await this.contextEngine.searchDocumentation(
          `${dbTech} database schema design best practices scalability`
        )
      } catch (error) {
        console.warn('Could not fetch database best practices:', error)
      }
    }

    const systemPrompt = `You are a senior database architect expert at designing scalable database schemas with access to real-time industry data.

    🧠 SEQUENTIAL THINKING CONTEXT:
    ${sequentialThought ? `Previous reasoning: ${JSON.stringify(sequentialThought, null, 2)}` : 'No sequential thinking context available'}

    📚 INDUSTRY ENRICHMENTS (${enrichments.length} available):
    ${enrichments.length > 0 ? enrichments.map(e => `- ${e.title}: ${e.description} (${e.impact} impact)`).join('\n') : 'No specific industry enrichments available'}

    🔍 REAL-TIME DATABASE BEST PRACTICES:
    ${databaseBestPractices ? JSON.stringify(databaseBestPractices, null, 2) : 'No real-time database practices available'}

    Create a comprehensive database schema design using the MCP context above.

    For agent builder platforms, consider:
    - Separating high-volume metrics from execution logs for performance
    - Agent templates vs deployed agent instances
    - User management and permissions
    - Resource monitoring and billing
    - Audit trails and compliance

    Return a JSON object with this EXACT structure:

    {
      "databaseType": "relational" | "document" | "graph" | "hybrid",
      "tables": [
        {
          "name": "table_name",
          "purpose": "What this table stores",
          "columns": [
            {
              "name": "column_name",
              "type": "VARCHAR(255)" | "INTEGER" | "BOOLEAN" | "TIMESTAMP" | "TEXT" | "JSON" | "other",
              "constraints": ["PRIMARY KEY", "NOT NULL", "UNIQUE", "FOREIGN KEY"],
              "description": "What this column represents"
            }
          ],
          "indexes": [
            {
              "name": "index_name",
              "columns": ["column1", "column2"],
              "type": "btree" | "hash" | "gin" | "gist",
              "purpose": "Why this index is needed based on MCP analysis"
            }
          ],
          "relationships": [
            {
              "type": "one-to-many" | "many-to-many" | "one-to-one",
              "relatedTable": "related_table_name",
              "foreignKey": "foreign_key_column",
              "description": "Relationship description"
            }
          ]
        }
      ],
      "views": [
        {
          "name": "view_name",
          "purpose": "What this view provides",
          "query": "SQL query or description",
          "tables": ["table1", "table2"]
        }
      ],
      "migrations": [
        {
          "version": "001",
          "description": "Initial schema creation",
          "operations": ["CREATE TABLE users", "CREATE INDEX idx_users_email"]
        }
      ],
      "performance": {
        "considerations": ["consideration1", "consideration2"],
        "optimizations": ["optimization1", "optimization2"],
        "scalingStrategy": "How to scale the database based on MCP insights"
      },
      "security": {
        "authentication": "How users are authenticated",
        "authorization": "How permissions are managed",
        "dataProtection": ["protection1", "protection2"],
        "compliance": ["GDPR", "HIPAA", "SOC2"]
      },
      "mcpEnhanced": {
        "enrichmentsUsed": ${enrichments.length},
        "sequentialThinkingApplied": ${!!sequentialThought},
        "bestPracticesApplied": ${!!databaseBestPractices}
      }
    }

    Consider the chosen technology stack, project requirements, scalability needs, AND the MCP context above.
    CRITICAL: Return ONLY the JSON object. Do not include any explanatory text, comments, or markdown formatting.`

    const response = await this.model!.invoke([
      new SystemMessage(systemPrompt),
      new HumanMessage(`Project: ${state.prompt}

Analysis: ${JSON.stringify(state.results.analyze)}
PRD: ${JSON.stringify(state.results.prd)}
Tech Stack: ${JSON.stringify(state.results.techstack)}

Design the database schema for this application using the MCP context.`),
    ])

    try {
      const database = JSON.parse(response.content as string)

      // Add MCP metadata
      database._mcpEnhanced = true
      database._enrichmentCount = enrichments.length
      database._hasSequentialThinking = !!sequentialThought
      database._hasBestPractices = !!databaseBestPractices

      return {
        ...state,
        results: {
          ...state.results,
          database: database,
        },
        needsInput: false,
        question: undefined,
      }
    } catch (error) {
      console.error('MCP database schema parsing failed:', error)
      // Enhanced fallback with MCP context
      return {
        ...state,
        results: {
          ...state.results,
          database: {
            databaseType: "relational",
            tables: [
              {
                name: "users",
                purpose: "Store user account and authentication information",
                columns: [
                  {
                    name: "id",
                    type: "UUID",
                    constraints: ["PRIMARY KEY"],
                    description: "Unique user identifier"
                  },
                  {
                    name: "email",
                    type: "VARCHAR(255)",
                    constraints: ["NOT NULL", "UNIQUE"],
                    description: "User email address"
                  },
                  {
                    name: "subscription_tier",
                    type: "VARCHAR(50)",
                    constraints: ["NOT NULL", "DEFAULT 'free'"],
                    description: "User subscription level"
                  },
                  {
                    name: "created_at",
                    type: "TIMESTAMP",
                    constraints: ["NOT NULL", "DEFAULT CURRENT_TIMESTAMP"],
                    description: "Account creation timestamp"
                  }
                ],
                indexes: [
                  {
                    name: "idx_users_email",
                    columns: ["email"],
                    type: "btree",
                    purpose: "Fast email lookups for authentication"
                  }
                ]
              },
              {
                name: "agent_templates",
                purpose: "Store reusable agent templates and configurations",
                columns: [
                  {
                    name: "id",
                    type: "UUID",
                    constraints: ["PRIMARY KEY"],
                    description: "Unique template identifier"
                  },
                  {
                    name: "name",
                    type: "VARCHAR(255)",
                    constraints: ["NOT NULL"],
                    description: "Template name"
                  },
                  {
                    name: "description",
                    type: "TEXT",
                    constraints: [],
                    description: "Template description"
                  },
                  {
                    name: "config_schema",
                    type: "JSONB",
                    constraints: ["NOT NULL"],
                    description: "LangGraph configuration schema"
                  },
                  {
                    name: "created_by",
                    type: "UUID",
                    constraints: ["FOREIGN KEY REFERENCES users(id)"],
                    description: "Template creator"
                  }
                ],
                indexes: [
                  {
                    name: "idx_templates_created_by",
                    columns: ["created_by"],
                    type: "btree",
                    purpose: "Fast lookup of user templates"
                  }
                ]
              },
              {
                name: "deployed_agents",
                purpose: "Store deployed agent instances and their runtime state",
                columns: [
                  {
                    name: "id",
                    type: "UUID",
                    constraints: ["PRIMARY KEY"],
                    description: "Unique deployed agent identifier"
                  },
                  {
                    name: "template_id",
                    type: "UUID",
                    constraints: ["FOREIGN KEY REFERENCES agent_templates(id)"],
                    description: "Reference to agent template"
                  },
                  {
                    name: "user_id",
                    type: "UUID",
                    constraints: ["FOREIGN KEY REFERENCES users(id)"],
                    description: "Agent owner"
                  },
                  {
                    name: "status",
                    type: "VARCHAR(50)",
                    constraints: ["NOT NULL", "DEFAULT 'stopped'"],
                    description: "Current agent status"
                  },
                  {
                    name: "e2b_environment_id",
                    type: "VARCHAR(255)",
                    constraints: [],
                    description: "E2B sandbox environment ID"
                  },
                  {
                    name: "deployed_at",
                    type: "TIMESTAMP",
                    constraints: ["NOT NULL", "DEFAULT CURRENT_TIMESTAMP"],
                    description: "Deployment timestamp"
                  }
                ],
                indexes: [
                  {
                    name: "idx_agents_user_status",
                    columns: ["user_id", "status"],
                    type: "btree",
                    purpose: "Fast lookup of user agents by status"
                  }
                ]
              },
              {
                name: "execution_logs",
                purpose: "Store agent execution logs and debugging information",
                columns: [
                  {
                    name: "id",
                    type: "UUID",
                    constraints: ["PRIMARY KEY"],
                    description: "Unique log entry identifier"
                  },
                  {
                    name: "agent_id",
                    type: "UUID",
                    constraints: ["FOREIGN KEY REFERENCES deployed_agents(id)"],
                    description: "Reference to deployed agent"
                  },
                  {
                    name: "log_level",
                    type: "VARCHAR(20)",
                    constraints: ["NOT NULL"],
                    description: "Log severity level"
                  },
                  {
                    name: "message",
                    type: "TEXT",
                    constraints: ["NOT NULL"],
                    description: "Log message content"
                  },
                  {
                    name: "metadata",
                    type: "JSONB",
                    constraints: [],
                    description: "Additional log metadata"
                  },
                  {
                    name: "timestamp",
                    type: "TIMESTAMP",
                    constraints: ["NOT NULL", "DEFAULT CURRENT_TIMESTAMP"],
                    description: "Log entry timestamp"
                  }
                ],
                indexes: [
                  {
                    name: "idx_logs_agent_timestamp",
                    columns: ["agent_id", "timestamp"],
                    type: "btree",
                    purpose: "Fast chronological log retrieval"
                  }
                ]
              },
              {
                name: "monitoring_metrics",
                purpose: "Store high-volume performance and usage metrics",
                columns: [
                  {
                    name: "id",
                    type: "UUID",
                    constraints: ["PRIMARY KEY"],
                    description: "Unique metric entry identifier"
                  },
                  {
                    name: "agent_id",
                    type: "UUID",
                    constraints: ["FOREIGN KEY REFERENCES deployed_agents(id)"],
                    description: "Reference to deployed agent"
                  },
                  {
                    name: "metric_type",
                    type: "VARCHAR(50)",
                    constraints: ["NOT NULL"],
                    description: "Type of metric (cpu, memory, requests, etc.)"
                  },
                  {
                    name: "value",
                    type: "DECIMAL(10,4)",
                    constraints: ["NOT NULL"],
                    description: "Metric value"
                  },
                  {
                    name: "unit",
                    type: "VARCHAR(20)",
                    constraints: [],
                    description: "Metric unit (%, MB, req/s, etc.)"
                  },
                  {
                    name: "recorded_at",
                    type: "TIMESTAMP",
                    constraints: ["NOT NULL", "DEFAULT CURRENT_TIMESTAMP"],
                    description: "Metric recording timestamp"
                  }
                ],
                indexes: [
                  {
                    name: "idx_metrics_agent_type_time",
                    columns: ["agent_id", "metric_type", "recorded_at"],
                    type: "btree",
                    purpose: "Fast metric queries and aggregations"
                  }
                ]
              }
            ],
            performance: {
              considerations: [
                "Separate monitoring_metrics from execution_logs for high-volume data",
                "Use UUID for better distribution and security",
                "JSONB for flexible configuration storage",
                "Partitioning strategy for time-series data"
              ],
              optimizations: [
                "Time-based partitioning for metrics and logs tables",
                "Connection pooling for E2B API calls",
                "Read replicas for analytics queries",
                "Automated archival of old metrics data"
              ]
            },
            _mcpEnhanced: true,
            _fallback: true,
            _enrichmentCount: enrichments.length,
            _tableCount: 5,
            _designPattern: "separated_concerns"
          },
        },
        needsInput: false,
        question: undefined,
      }
    }
  }

  private async planFilesystemWithMCP(state: any): Promise<PlanningState> {
    console.log('📁 Using MCP-enhanced filesystem planning...')
    return await this.planFilesystem(state)
  }

  private async defineWorkflowWithMCP(state: any): Promise<PlanningState> {
    console.log('🔄 Using MCP-enhanced workflow definition...')
    return await this.defineWorkflow(state)
  }

  private async breakdownTasksWithMCP(state: any): Promise<PlanningState> {
    console.log('📋 Using MCP-enhanced task breakdown...')
    return await this.breakdownTasks(state)
  }

  /**
   * Generate project scaffold (missing method)
   */
  private async generateScaffold(state: PlanningState): Promise<PlanningState> {
    this.ensureModel();
    const systemPrompt = `Generate a project scaffold structure.

    Project: ${state.prompt}
    Analysis: ${JSON.stringify(state.results.analyze)}
    Tech Stack: ${JSON.stringify(state.results.techstack)}
    Filesystem: ${JSON.stringify(state.results.filesystem)}

    Generate scaffold in JSON format with keys: structure, commands, setup`

    const response = await this.model!.invoke([
      new SystemMessage(systemPrompt),
      new HumanMessage("Generate the project scaffold for this application."),
    ])

    try {
      const scaffold = JSON.parse(response.content as string)

      return {
        ...state,
        results: {
          ...state.results,
          scaffold: scaffold,
        },
        needsInput: false,
        question: undefined,
      }
    } catch (error) {
      return {
        ...state,
        results: {
          ...state.results,
          scaffold: {
            structure: "Basic project structure",
            commands: ["npm install", "npm run dev"],
            setup: "Standard setup process",
          },
        },
        needsInput: false,
        question: undefined,
      }
    }
  }

  /**
   * MCP-Enhanced Project Scaffold Generation with Real-time Code Patterns
   */
  private async generateScaffoldWithMCP(state: any): Promise<PlanningState> {
    this.ensureModel();

    const mcpContext = state.mcpContext
    const sequentialThought = mcpContext?.sequentialThought
    const enrichments = mcpContext?.enhancedContext?.enrichments || []

    // Get real-time scaffolding best practices
    let scaffoldBestPractices = null
    if (this.contextEngine) {
      try {
        const techStack = state.results?.techstack
        const frontendTech = typeof techStack?.frontend === 'string'
          ? techStack.frontend
          : techStack?.frontend?.framework || 'React'
        scaffoldBestPractices = await this.contextEngine.searchDocumentation(
          `${frontendTech} project scaffold structure best practices`
        )
      } catch (error) {
        console.warn('Could not fetch scaffold best practices:', error)
      }
    }

    const systemPrompt = `You are a senior software engineer expert at project scaffolding and code generation with access to real-time development patterns.

    🧠 SEQUENTIAL THINKING CONTEXT:
    ${sequentialThought ? `Previous reasoning: ${JSON.stringify(sequentialThought, null, 2)}` : 'No sequential thinking context available'}

    📚 INDUSTRY ENRICHMENTS (${enrichments.length} available):
    ${enrichments.length > 0 ? enrichments.map(e => `- ${e.title}: ${e.description} (${e.impact} impact)`).join('\n') : 'No specific industry enrichments available'}

    🔍 REAL-TIME SCAFFOLD BEST PRACTICES:
    ${scaffoldBestPractices ? JSON.stringify(scaffoldBestPractices, null, 2) : 'No real-time scaffold practices available'}

    Generate a comprehensive project scaffold with actual code files using the MCP context above.

    Return a JSON object with this EXACT structure:

    {
      "projectStructure": {
        "rootFiles": [
          {
            "name": "package.json",
            "content": "actual file content as string",
            "description": "Package configuration with dependencies"
          }
        ],
        "folders": [
          {
            "name": "src",
            "files": [
              {
                "name": "index.js",
                "content": "actual code content",
                "description": "Main application entry point"
              }
            ]
          }
        ]
      },
      "setupInstructions": [
        {
          "step": 1,
          "title": "Install Dependencies",
          "command": "npm install",
          "description": "Install all required packages"
        }
      ],
      "environmentSetup": {
        "envVariables": [
          {
            "name": "DATABASE_URL",
            "description": "Database connection string",
            "example": "postgresql://user:pass@localhost:5432/dbname",
            "required": true
          }
        ]
      },
      "scripts": {
        "development": [
          {
            "name": "dev",
            "command": "npm run dev",
            "description": "Start development server"
          }
        ]
      },
      "documentation": {
        "readme": "Complete README.md content with setup instructions",
        "deploymentGuide": "Step-by-step deployment instructions"
      },
      "nextSteps": [
        "Configure environment variables",
        "Set up database",
        "Start development server"
      ],
      "mcpEnhanced": {
        "enrichmentsUsed": ${enrichments.length},
        "sequentialThinkingApplied": ${!!sequentialThought},
        "scaffoldBestPracticesApplied": ${!!scaffoldBestPractices}
      }
    }

    IMPORTANT:
    - Generate actual, working code content for files (not descriptions)
    - Include complete Prisma schemas, Redux slices, API routes, components
    - Provide copy-paste ready code that developers can use immediately
    - Use the MCP context and enrichments to inform your scaffolding decisions
    - Consider real-time best practices in your code structure
    - For agent builder platforms, include: agent templates, execution engines, monitoring
    CRITICAL: Return ONLY the JSON object. Do not include any explanatory text.`

    const response = await this.model!.invoke([
      new SystemMessage(systemPrompt),
      new HumanMessage(`Project: ${state.prompt}

Analysis: ${JSON.stringify(state.results.analyze)}
Tech Stack: ${JSON.stringify(state.results.techstack)}
Filesystem: ${JSON.stringify(state.results.filesystem)}
Database: ${JSON.stringify(state.results.database)}

Generate a comprehensive project scaffold using the MCP context.`),
    ])

    try {
      const scaffold = JSON.parse(response.content as string)

      // Add MCP metadata
      scaffold._mcpEnhanced = true
      scaffold._enrichmentCount = enrichments.length
      scaffold._hasSequentialThinking = !!sequentialThought
      scaffold._hasScaffoldBestPractices = !!scaffoldBestPractices

      return {
        ...state,
        results: {
          ...state.results,
          scaffold: scaffold,
        },
        needsInput: false,
        question: undefined,
      }
    } catch (error) {
      console.error('MCP scaffold parsing failed:', error)
      // Fallback to original method
      return await this.generateScaffold(state)
    }
  }
}
