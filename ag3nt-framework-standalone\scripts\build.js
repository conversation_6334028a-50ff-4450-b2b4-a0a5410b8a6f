#!/usr/bin/env node

/**
 * AG3NT Framework - Build Script
 * 
 * Builds the framework for distribution
 */

const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

console.log('🔨 Building AG3NT Framework...')

try {
  // Clean previous build
  console.log('🧹 Cleaning previous build...')
  if (fs.existsSync('dist')) {
    fs.rmSync('dist', { recursive: true, force: true })
  }

  // Build TypeScript
  console.log('📦 Compiling TypeScript...')
  execSync('npx tsc', { stdio: 'inherit' })

  // Copy assets
  console.log('📋 Copying assets...')
  const assetsDir = path.join('src', 'assets')
  if (fs.existsSync(assetsDir)) {
    execSync(`cp -r "${assetsDir}" dist/`, { stdio: 'inherit' })
  }

  // Copy package.json fields to dist
  console.log('📄 Preparing package metadata...')
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'))
  const distPackageJson = {
    name: packageJson.name,
    version: packageJson.version,
    description: packageJson.description,
    main: packageJson.main,
    types: packageJson.types,
    exports: packageJson.exports,
    keywords: packageJson.keywords,
    author: packageJson.author,
    license: packageJson.license,
    repository: packageJson.repository,
    bugs: packageJson.bugs,
    homepage: packageJson.homepage,
    engines: packageJson.engines,
    dependencies: packageJson.dependencies,
    peerDependencies: packageJson.peerDependencies,
    optionalDependencies: packageJson.optionalDependencies
  }

  fs.writeFileSync(
    path.join('dist', 'package.json'),
    JSON.stringify(distPackageJson, null, 2)
  )

  // Copy essential files
  console.log('📋 Copying essential files...')
  const filesToCopy = ['README.md', 'LICENSE', 'CHANGELOG.md']
  for (const file of filesToCopy) {
    if (fs.existsSync(file)) {
      fs.copyFileSync(file, path.join('dist', file))
    }
  }

  console.log('✅ Build completed successfully!')
  console.log('📦 Distribution ready in ./dist/')

} catch (error) {
  console.error('❌ Build failed:', error.message)
  process.exit(1)
}
