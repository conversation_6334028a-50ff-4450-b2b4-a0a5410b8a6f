{"name": "ag3nt-framework", "version": "1.0.0", "description": "The world's most advanced multi-agent development framework - surpassing CrewAI and LangGraph with enterprise-grade autonomous development capabilities", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.js", "types": "./dist/index.d.ts"}, "./core": {"import": "./dist/core/index.js", "require": "./dist/core/index.js", "types": "./dist/core/index.d.ts"}, "./agents": {"import": "./dist/agents/index.js", "require": "./dist/agents/index.js", "types": "./dist/agents/index.d.ts"}, "./workflows": {"import": "./dist/workflows/index.js", "require": "./dist/workflows/index.js", "types": "./dist/workflows/index.d.ts"}, "./coordination": {"import": "./dist/coordination/index.js", "require": "./dist/coordination/index.js", "types": "./dist/coordination/index.d.ts"}, "./discovery": {"import": "./dist/discovery/index.js", "require": "./dist/discovery/index.js", "types": "./dist/discovery/index.d.ts"}, "./advanced": {"import": "./dist/advanced/index.js", "require": "./dist/advanced/index.js", "types": "./dist/advanced/index.d.ts"}}, "files": ["dist", "README.md", "LICENSE", "CHANGELOG.md"], "scripts": {"build": "tsc && npm run build:copy-assets", "build:dev": "tsc --watch", "build:copy-assets": "cp -r src/**/*.json dist/ 2>/dev/null || true", "clean": "rm -rf dist", "dev": "tsx src/index.ts", "test": "jest", "test:unit": "jest --testPathPattern=unit", "test:integration": "jest --testPathPattern=integration", "test:e2e": "jest --testPathPattern=e2e", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "demo:master": "tsx examples/ag3nt-framework-master-demo.ts", "demo:workflows": "tsx examples/multi-agent-workflow-demo.ts", "demo:coordination": "tsx examples/coordination-patterns-demo.ts", "demo:discovery": "tsx examples/discovery-load-balancing-demo.ts", "demo:benchmarks": "tsx benchmarks/framework-comparison.ts", "benchmark": "npm run demo:benchmarks", "docs:generate": "typedoc src/index.ts --out docs/api", "docs:serve": "http-server docs -p 8080", "prepublishOnly": "npm run clean && npm run build && npm test", "release": "npm version patch && npm publish", "release:minor": "npm version minor && npm publish", "release:major": "npm version major && npm publish"}, "keywords": ["multi-agent", "autonomous-development", "ai-agents", "workflow-orchestration", "agent-coordination", "load-balancing", "enterprise-ai", "development-automation", "intelligent-agents", "agent-framework", "crewai-alternative", "langgraph-alternative", "autonomous-coding", "ai-development", "agent-discovery", "workflow-engine", "coordination-patterns", "adaptive-learning", "real-time-analytics", "enterprise-grade"], "author": "AG3NT Team", "license": "PROPRIETARY", "repository": {"type": "git", "url": "https://github.com/ag3nt/ag3nt-framework.git"}, "bugs": {"url": "https://github.com/ag3nt/ag3nt-framework/issues"}, "homepage": "https://github.com/ag3nt/ag3nt-framework#readme", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "dependencies": {"events": "^3.3.0", "uuid": "^9.0.0", "neo4j-driver": "^5.15.0", "pg": "^8.11.0", "redis": "^4.6.0", "ws": "^8.14.0", "zod": "^3.22.0", "lodash": "^4.17.21", "axios": "^1.6.0", "jsonwebtoken": "^9.0.0", "bcryptjs": "^2.4.3", "dotenv": "^16.3.0", "chalk": "^5.3.0", "ora": "^7.0.0", "inquirer": "^9.2.0"}, "devDependencies": {"@types/node": "^20.10.0", "@types/uuid": "^9.0.0", "@types/pg": "^8.10.0", "@types/ws": "^8.5.0", "@types/lodash": "^4.14.0", "@types/jsonwebtoken": "^9.0.0", "@types/bcryptjs": "^2.4.0", "@types/inquirer": "^9.0.0", "@types/jest": "^29.5.0", "typescript": "^5.3.0", "tsx": "^4.6.0", "jest": "^29.7.0", "ts-jest": "^29.1.0", "eslint": "^8.55.0", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "prettier": "^3.1.0", "typedoc": "^0.25.0", "http-server": "^14.1.0", "nodemon": "^3.0.0", "concurrently": "^8.2.0"}, "peerDependencies": {"typescript": ">=5.0.0"}, "optionalDependencies": {"neo4j-driver": "^5.15.0", "pg": "^8.11.0", "redis": "^4.6.0"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "roots": ["<rootDir>/src", "<rootDir>/tests"], "testMatch": ["**/__tests__/**/*.ts", "**/?(*.)+(spec|test).ts"], "transform": {"^.+\\.ts$": "ts-jest"}, "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts", "!src/**/*.test.ts", "!src/**/*.spec.ts"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"]}, "eslintConfig": {"parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint"], "extends": ["eslint:recommended", "@typescript-eslint/recommended"], "rules": {"@typescript-eslint/no-unused-vars": "error", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/explicit-function-return-type": "warn"}}, "prettier": {"semi": false, "singleQuote": true, "tabWidth": 2, "trailingComma": "es5", "printWidth": 100}, "publishConfig": {"access": "restricted"}}