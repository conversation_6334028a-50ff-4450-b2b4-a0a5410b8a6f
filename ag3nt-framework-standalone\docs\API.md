# AG3NT Framework - Core API Documentation

The AG3NT Framework provides multiple API layers to suit different use cases and complexity levels.

## API Layers

### 1. Simple API (Recommended for most use cases)
Easy-to-use interface with sensible defaults and common operations.

```typescript
import { quickStart, quickPlan, SimpleAPI } from '@/lib/ag3nt-framework'

// Ultra-simple: Plan a project in one line
const result = await quickPlan('Build a social media app')

// Simple API with more control
const api = await quickStart()
const planResult = await api.planProject('Build a todo app', {
  priority: 'high',
  interactive: false
})
```

### 2. Full Framework API
Complete type-safe interface for all framework operations.

```typescript
import { createAPI, createFramework } from '@/lib/ag3nt-framework'

const framework = createFramework()
const api = createAPI(framework)

await api.initialize({
  context: { enableMCP: true },
  communication: { enableRealTime: true },
  workflows: { enablePersistence: true }
})
```

### 3. Direct Framework Access
Low-level access to framework internals for advanced use cases.

```typescript
import { AG3NTFramework } from '@/lib/ag3nt-framework'

const framework = new AG3NTFramework(config)
await framework.initialize()
```

## Core Operations

### Agent Management

#### Register Agents
```typescript
// Simple registration
const agentId = await api.createPlanningAgent({
  name: 'Project Planner',
  description: 'AI-powered project planning',
  tags: ['planning', 'analysis']
})

// Custom agent registration
const customAgentId = await api.registerAgent(customAgent, {
  description: 'Custom development agent',
  tags: ['coding', 'frontend'],
  priority: 8,
  autoStart: true
})
```

#### Execute Tasks
```typescript
// Execute with agent type
const result = await api.execute('planning-agent', {
  prompt: 'Build a social media app',
  isInteractive: false
}, {
  priority: 'high',
  timeout: 300000
})

// Execute specific agent
const specificResult = await api.agents.executeById(agentId, input, options)
```

#### Monitor Agents
```typescript
// Get all agents
const agents = await api.getAgents()

// Get agent health
const health = await api.getAgentHealth(agentId)

// Get agent metrics
const metrics = await api.agents.getMetrics(agentId)
```

### Communication

#### Messaging
```typescript
// Send direct message
const messageId = await api.sendMessage(
  'agent-1', 
  'agent-2', 
  { task: 'process_data', data: payload },
  { priority: 'high', requiresResponse: true }
)

// Broadcast to all agents
const broadcastId = await api.broadcast(
  'coordinator',
  { event: 'system_update', version: '1.2.0' },
  { priority: 'normal' }
)
```

#### Channels
```typescript
// Create communication channel
const channelId = await api.createChannel('frontend-team', {
  purpose: 'Frontend development coordination',
  persistent: true,
  maxParticipants: 5
})

// Join/leave channels
await api.communication.joinChannel(channelId, agentId)
await api.communication.leaveChannel(channelId, agentId)
```

#### Presence Management
```typescript
// Get agent presence
const presence = await api.communication.getPresence(agentId)

// Update presence
await api.communication.updatePresence(agentId, {
  status: 'busy',
  metadata: { load: 0.8, availability: 0.2 }
})
```

### Workflows

#### Create Workflows
```typescript
// Simple workflow creation
await api.createWorkflow('Development Pipeline', [
  { name: 'Plan Project', agentType: 'planning-agent' },
  { name: 'Generate Frontend', agentType: 'frontend-agent', dependencies: ['step-1'] },
  { name: 'Generate Backend', agentType: 'backend-agent', dependencies: ['step-1'] },
  { name: 'Run Tests', agentType: 'testing-agent', dependencies: ['step-2', 'step-3'] }
])

// Advanced workflow registration
await api.workflows.register({
  workflowId: 'custom-workflow',
  name: 'Custom Development Workflow',
  steps: [...],
  errorHandling: { strategy: 'continue', maxRetries: 3 }
})
```

#### Execute Workflows
```typescript
// Execute workflow
const executionId = await api.executeWorkflow('development-pipeline', {
  prompt: 'Build a social media app',
  requirements: ['responsive', 'real-time']
}, {
  priority: 'high',
  tags: ['production']
})

// Monitor execution
const status = await api.getWorkflowStatus(executionId)
console.log(`Progress: ${status.progress}% (${status.completedSteps}/${status.totalSteps})`)
```

### Context Management

#### Context Operations
```typescript
// Store and retrieve context
await api.context.set('project-config', config, { 
  scope: 'session', 
  ttl: 3600000 
})
const config = await api.context.get('project-config')

// Shared state management
await api.context.setSharedState('team-preferences', preferences)
const teamPrefs = await api.context.getSharedState('team-preferences')

// Memory operations
await api.context.remember('user-preferences', userPrefs, 86400000) // 24 hours
const savedPrefs = await api.context.recall('user-preferences')
```

#### Context Enhancement
```typescript
// Enhance context with MCP and RAG
const enhanced = await api.context.enhance(baseContext, {
  enableMCP: true,
  enableRAG: true,
  maxEnrichments: 10
})

// Enrich specific data
const enriched = await api.context.enrich(projectData, 'best-practices')
```

### Monitoring & Observability

#### Health Monitoring
```typescript
// Framework health
const health = await api.getHealth()
console.log(`Framework status: ${health.status}`)

// Component health
const agentHealth = await api.monitoring.getComponentHealth('agents')
const commHealth = await api.monitoring.getComponentHealth('communication')
```

#### Metrics & Performance
```typescript
// Get comprehensive metrics
const metrics = await api.getMetrics()
console.log(`Active agents: ${metrics.agents.active}`)
console.log(`Messages/sec: ${metrics.communication.messagesPerSecond}`)

// Record custom metrics
await api.monitoring.recordMetric('custom.processing_time', 1250, {
  agent: 'planning-agent',
  operation: 'analyze'
})

// Performance profiling
const sessionId = await api.monitoring.startProfiling({
  duration: 60000,
  includeMemory: true,
  includeCPU: true
})
// ... perform operations ...
const profile = await api.monitoring.stopProfiling(sessionId)
```

#### Simple Status Overview
```typescript
// Get simple status
const status = await api.getStatus()
console.log(`Healthy: ${status.healthy}`)
console.log(`Agents: ${status.agents.active}/${status.agents.total}`)
console.log(`Uptime: ${status.uptime}s`)
```

## Configuration

### Framework Configuration
```typescript
await api.initialize({
  agents: {
    autoRegisterBuiltins: true,
    maxConcurrentSessions: 10,
    defaultTimeout: 300000
  },
  communication: {
    enableRealTime: true,
    messageRetention: 1000,
    heartbeatInterval: 30000
  },
  workflows: {
    enablePersistence: true,
    maxConcurrentExecutions: 5,
    defaultTimeout: 1800000
  },
  context: {
    enableMCP: true,
    enableRAG: true,
    enableEnrichment: true
  },
  monitoring: {
    enableMetrics: true,
    enableTracing: true,
    enableProfiling: false
  }
})
```

### Runtime Configuration
```typescript
// Feature flags
api.config.enableFeature('advanced-planning')
api.config.disableFeature('experimental-rag')

// Environment checks
if (api.config.isProduction()) {
  // Production-specific configuration
}

// Configuration validation
const validation = api.config.validate()
if (!validation.valid) {
  console.error('Configuration errors:', validation.errors)
}
```

## Extensions & Plugins

### Plugin Development
```typescript
class CustomPlugin implements IPlugin {
  readonly id = 'custom-plugin'
  readonly name = 'Custom Plugin'
  readonly version = '1.0.0'
  readonly description = 'Custom functionality plugin'
  readonly dependencies = []

  async initialize(framework: IFrameworkAPI): Promise<void> {
    // Plugin initialization
  }

  async shutdown(): Promise<void> {
    // Cleanup
  }

  configure(config: any): void {
    // Configuration
  }

  getConfig(): any {
    return this.config
  }

  async getHealth(): Promise<PluginHealth> {
    return { status: 'healthy', lastCheck: new Date().toISOString(), errorCount: 0 }
  }
}

// Register plugin
await api.extensions.register(new CustomPlugin())
```

### Middleware
```typescript
class LoggingMiddleware implements IMiddleware {
  readonly id = 'logging-middleware'
  readonly priority = 1

  async process(context: MiddlewareContext, next: () => Promise<any>): Promise<any> {
    console.log(`Processing request: ${context.request.type}`)
    const result = await next()
    console.log(`Request completed: ${context.request.type}`)
    return result
  }
}

api.extensions.addMiddleware(new LoggingMiddleware())
```

### Hooks
```typescript
// Add event hooks
api.extensions.addHook('agent_registered', async (data) => {
  console.log(`New agent registered: ${data.agentType}`)
})

api.extensions.addHook('execution_completed', async (data) => {
  console.log(`Execution completed: ${data.executionId}`)
})
```

## Error Handling

### Graceful Error Handling
```typescript
try {
  const result = await api.execute('planning-agent', input)
  console.log('Success:', result)
} catch (error) {
  if (error.message.includes('Agent not found')) {
    // Handle missing agent
    console.log('Creating planning agent...')
    await api.createPlanningAgent()
    // Retry execution
  } else {
    console.error('Execution failed:', error)
  }
}
```

### Health Monitoring
```typescript
// Monitor framework health
setInterval(async () => {
  const health = await api.getHealth()
  if (health.status !== 'healthy') {
    console.warn('Framework health degraded:', health)
    // Take corrective action
  }
}, 30000)
```

## Best Practices

1. **Use Simple API for common operations** - Start with `quickStart()` and `SimpleAPI`
2. **Initialize once** - Create framework instance once and reuse
3. **Handle errors gracefully** - Always wrap API calls in try-catch
4. **Monitor health** - Regularly check framework and agent health
5. **Use appropriate timeouts** - Set realistic timeouts for long-running operations
6. **Tag operations** - Use tags for better organization and filtering
7. **Clean up resources** - Always call `shutdown()` when done
8. **Use workflows for complex operations** - Break down complex tasks into workflows
9. **Leverage context enhancement** - Use MCP and RAG for better results
10. **Monitor performance** - Use metrics and profiling for optimization

## Migration Guide

### From Direct Framework Usage
```typescript
// Old way
const framework = new AG3NTFramework()
await framework.initialize()
const result = await framework.execute('planning-agent', input)

// New way
const api = await quickStart()
const result = await api.planProject(input.prompt)
```

### From Custom Implementations
```typescript
// Old way - custom agent management
const agents = new Map()
agents.set(agentId, agent)

// New way - use framework registry
const agentId = await api.registerAgent(agent)
const registeredAgent = await api.agents.get(agentId)
```

This API provides a comprehensive, type-safe interface for all AG3NT Framework operations while maintaining ease of use for common scenarios.
