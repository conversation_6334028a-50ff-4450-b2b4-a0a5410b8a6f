/**
 * AG3NT Framework - Planning Agent
 * 
 * Concrete implementation of BaseAgent for project planning.
 * Extracted from the sophisticated planning-graph.ts implementation.
 * 
 * Features:
 * - Complete project analysis and planning workflow
 * - MCP-enhanced execution with context enrichment
 * - Sequential thinking for complex decisions
 * - Dynamic step sequencing based on project type
 * - Interactive clarification handling
 * - Progress tracking and session management
 */

import { BaseAgent, AgentState, AgentConfig } from "../core/base-agent"
import { aiService } from "../../ai-service"

export interface PlanningInput {
  prompt: string
  isInteractive?: boolean
  userAnswers?: Record<string, string>
  designStyleGuide?: any
  hasImages?: boolean
  userPreferences?: {
    model?: string
    apiKey?: string
  }
}

export interface PlanningResult {
  analyze?: any
  clarify?: any
  summary?: any
  techstack?: any
  prd?: any
  'context-profile'?: any
  wireframes?: any
  design?: any
  database?: any
  filesystem?: any
  workflow?: any
  tasks?: any
  scaffold?: any
}

/**
 * Planning Agent - Sophisticated project planning with MCP enhancement
 */
export class PlanningAgent extends BaseAgent {
  private readonly planningSteps = [
    'analyze', 'clarify', 'summary', 'techstack', 'prd', 
    'context-profile', 'wireframes', 'design', 'database', 
    'filesystem', 'workflow', 'tasks', 'scaffold'
  ]

  constructor(config: Partial<AgentConfig> = {}) {
    super('planning-agent', {
      capabilities: {
        requiredCapabilities: [
          'requirements_analysis', 
          'tech_stack_selection', 
          'architecture_design', 
          'cross_validation'
        ],
        contextFilters: ['all'], // Planning agent needs full context
        mcpEnhanced: true,
        sequentialThinking: true,
        contextEnrichment: true,
        ragIntegration: true
      },
      ...config
    })
  }

  /**
   * Execute planning workflow - extracted from planning-graph.ts execute()
   */
  protected async executeWorkflow(state: AgentState): Promise<AgentState> {
    const input = state.input as PlanningInput
    
    console.log(`🚀 Starting planning workflow for: ${input.prompt.substring(0, 100)}...`)

    // Always start with analyze step
    let currentStep = 'analyze'
    const result = await this.executeStepWithContext(currentStep, input)
    
    if (result.needsInput) {
      // Interactive mode - return for user input
      return state
    }

    // Determine dynamic step sequence based on project type
    const projectType = state.results.analyze?.projectType
    const stepSequence = this.getStepsForProjectType(projectType)
    
    console.log(`📋 Executing ${stepSequence.length} steps for ${projectType} project`)

    // Execute remaining steps sequentially
    for (const stepId of stepSequence.slice(1)) {
      if (state.needsInput) {
        break // Stop if user input is needed
      }

      await this.executeStepWithContext(stepId)
      
      if (state.needsInput) {
        break
      }
    }

    // Mark as completed if no input needed
    if (!state.needsInput) {
      state.completed = true
    }

    return state
  }

  /**
   * Execute individual planning step - extracted from planning-graph.ts patterns
   */
  protected async executeStep(stepId: string, context: any): Promise<any> {
    const { stepInput, mcpContext, agentState } = context
    const input = stepInput as PlanningInput
    
    console.log(`🧠 Executing planning step: ${stepId}`)

    // Build enhanced context for AI execution
    const enhancedState = {
      ...agentState,
      mcpContext
    }

    // Execute step with MCP enhancement
    switch (stepId) {
      case 'analyze':
        return await this.analyzePromptWithMCP(enhancedState)
      case 'clarify':
        return await this.clarifyRequirementsWithMCP(enhancedState)
      case 'summary':
        return await this.generateSummaryWithMCP(enhancedState)
      case 'techstack':
        return await this.selectTechStackWithMCP(enhancedState)
      case 'prd':
        return await this.createPRDWithMCP(enhancedState)
      case 'context-profile':
        return await this.generateContextProfileWithMCP(enhancedState)
      case 'wireframes':
        return await this.designWireframesWithMCP(enhancedState)
      case 'design':
        return await this.createDesignGuidelinesWithMCP(enhancedState)
      case 'database':
        return await this.designDatabaseSchemaWithMCP(enhancedState)
      case 'filesystem':
        return await this.planFilesystemWithMCP(enhancedState)
      case 'workflow':
        return await this.defineWorkflowWithMCP(enhancedState)
      case 'tasks':
        return await this.breakdownTasksWithMCP(enhancedState)
      case 'scaffold':
        return await this.generateScaffoldWithMCP(enhancedState)
      default:
        throw new Error(`Unknown planning step: ${stepId}`)
    }
  }

  /**
   * Get total steps for progress tracking
   */
  protected getTotalSteps(): number {
    return this.planningSteps.length
  }

  /**
   * Get relevant documentation for step
   */
  protected async getRelevantDocumentation(stepId: string): Promise<Record<string, any>> {
    const relevantDocs: Record<string, any> = {}
    
    if (!this.state?.results.techstack) {
      return relevantDocs
    }

    const techStack = this.state.results.techstack

    // Get documentation for frontend technology
    if (techStack.frontend && this.contextEngine) {
      const frontendTech = typeof techStack.frontend === 'string'
        ? techStack.frontend
        : techStack.frontend.framework || techStack.frontend.name || 'React'
      
      relevantDocs.frontend = await this.contextEngine.getDocumentation(
        frontendTech,
        `${stepId} implementation`
      )
    }

    // Get documentation for backend technology
    if (techStack.backend && this.contextEngine) {
      const backendTech = typeof techStack.backend === 'string'
        ? techStack.backend
        : techStack.backend.framework || techStack.backend.name || 'Node.js'
      
      relevantDocs.backend = await this.contextEngine.getDocumentation(
        backendTech,
        `${stepId} implementation`
      )
    }

    return relevantDocs
  }

  /**
   * Determine step sequence based on project type
   */
  private getStepsForProjectType(projectType: string): string[] {
    const baseSteps = ['analyze', 'clarify', 'summary', 'techstack', 'prd']
    
    switch (projectType) {
      case 'web_app':
      case 'mobile_app':
        return [
          ...baseSteps,
          'context-profile',
          'wireframes',
          'design',
          'database',
          'filesystem',
          'workflow',
          'tasks',
          'scaffold'
        ]
      
      case 'api':
      case 'microservice':
        return [
          ...baseSteps,
          'context-profile',
          'database',
          'filesystem',
          'workflow',
          'tasks',
          'scaffold'
        ]
      
      case 'cli_tool':
      case 'library':
        return [
          ...baseSteps,
          'context-profile',
          'filesystem',
          'workflow',
          'tasks',
          'scaffold'
        ]
      
      default:
        return this.planningSteps // Full sequence for unknown types
    }
  }

  // MCP-Enhanced step implementations (delegating to aiService for now)
  private async analyzePromptWithMCP(state: any): Promise<any> {
    const input = state.input as PlanningInput
    const analysis = await aiService.analyzePrompt(input.prompt, {
      designStyleGuide: input.designStyleGuide,
      hasImages: input.hasImages
    })

    return {
      results: analysis,
      needsInput: false,
      completed: false
    }
  }

  private async clarifyRequirementsWithMCP(state: any): Promise<any> {
    const input = state.input as PlanningInput
    const analysis = state.results.analyze
    const userAnswers = input.userAnswers || {}

    // Check if we have all required answers
    const requiredAnswers = ['target_users', 'platform', 'timeline', 'budget']
    const missingAnswers = requiredAnswers.filter(key => !userAnswers[key])

    if (missingAnswers.length === 0) {
      return {
        results: {
          targetUsers: userAnswers.target_users,
          platform: userAnswers.platform,
          timeline: userAnswers.timeline,
          budget: userAnswers.budget,
          completed: true
        },
        needsInput: false,
        completed: false
      }
    }

    // Generate clarification questions
    const clarificationData = await aiService.generateClarificationQuestions(input.prompt, analysis)
    const nextQuestion = clarificationData.questions.find((q: any) => 
      !userAnswers[q.id] && missingAnswers.includes(q.id)
    ) || clarificationData.questions[0]

    return {
      results: clarificationData,
      question: nextQuestion,
      needsInput: true,
      completed: false
    }
  }

  private async generateSummaryWithMCP(state: any): Promise<any> {
    const input = state.input as PlanningInput
    const analysis = state.results.analyze
    const clarifications = state.results.clarify

    const summary = await aiService.generateSummary(input.prompt, analysis, clarifications)

    return {
      results: summary,
      needsInput: false,
      completed: false
    }
  }

  private async selectTechStackWithMCP(state: any): Promise<any> {
    const input = state.input as PlanningInput
    const analysis = state.results.analyze
    const clarifications = state.results.clarify

    const techStack = await aiService.selectTechStack(input.prompt, analysis, clarifications)

    return {
      results: techStack,
      needsInput: false,
      completed: false
    }
  }

  private async createPRDWithMCP(state: any): Promise<any> {
    const input = state.input as PlanningInput
    const previousResults = state.results

    const prd = await aiService.createPRD(
      input.prompt,
      previousResults.analyze,
      previousResults.techstack,
      previousResults.clarify
    )

    return {
      results: prd,
      needsInput: false,
      completed: false
    }
  }

  private async generateContextProfileWithMCP(state: any): Promise<any> {
    const input = state.input as PlanningInput
    const previousResults = state.results

    const contextProfile = await aiService.generateContextProfile(
      input.prompt,
      previousResults.analyze,
      previousResults.summary,
      previousResults.prd
    )

    return {
      results: contextProfile,
      needsInput: false,
      completed: false
    }
  }

  private async designWireframesWithMCP(state: any): Promise<any> {
    const input = state.input as PlanningInput
    const previousResults = state.results

    const wireframes = await aiService.designWireframes(
      input.prompt,
      previousResults.analyze,
      previousResults.prd
    )

    return {
      results: wireframes,
      needsInput: false,
      completed: false
    }
  }

  private async createDesignGuidelinesWithMCP(state: any): Promise<any> {
    // For now, return basic design guidelines
    const designGuidelines = {
      colorPalette: ["#1f2937", "#3b82f6", "#10b981", "#f59e0b"],
      typography: "Modern sans-serif fonts",
      spacing: "8px grid system",
      components: "Consistent button styles, form inputs, cards"
    }

    return {
      results: designGuidelines,
      needsInput: false,
      completed: false
    }
  }

  private async designDatabaseSchemaWithMCP(state: any): Promise<any> {
    const input = state.input as PlanningInput
    const previousResults = state.results

    const schema = await aiService.designDatabaseSchema(
      input.prompt,
      previousResults.analyze,
      previousResults.prd,
      previousResults.techstack
    )

    return {
      results: schema,
      needsInput: false,
      completed: false
    }
  }

  private async planFilesystemWithMCP(state: any): Promise<any> {
    const input = state.input as PlanningInput
    const previousResults = state.results

    const filesystem = await aiService.planFileSystem(
      input.prompt,
      previousResults.techstack,
      previousResults.analyze,
      previousResults.design
    )

    return {
      results: filesystem,
      needsInput: false,
      completed: false
    }
  }

  private async defineWorkflowWithMCP(state: any): Promise<any> {
    const input = state.input as PlanningInput
    const previousResults = state.results

    const workflow = await aiService.defineWorkflow(
      input.prompt,
      previousResults.analyze,
      previousResults.prd,
      previousResults.wireframes,
      previousResults.design
    )

    return {
      results: workflow,
      needsInput: false,
      completed: false
    }
  }

  private async breakdownTasksWithMCP(state: any): Promise<any> {
    const input = state.input as PlanningInput
    const previousResults = state.results

    const tasks = await aiService.breakdownTasks(input.prompt, previousResults, previousResults.design)

    return {
      results: tasks,
      needsInput: false,
      completed: false
    }
  }

  private async generateScaffoldWithMCP(state: any): Promise<any> {
    const input = state.input as PlanningInput
    const previousResults = state.results

    const scaffold = await aiService.generateProjectScaffold(
      input.prompt,
      previousResults.analyze,
      previousResults.techstack,
      previousResults.filesystem,
      previousResults.database
    )

    return {
      results: scaffold,
      needsInput: false,
      completed: true // Final step
    }
  }
}

// Export for easy access
export { PlanningAgent as default }
