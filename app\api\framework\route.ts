/**
 * AG3NT Platform - Framework API Routes
 * 
 * API endpoints for framework integration
 */

import { NextRequest, NextResponse } from 'next/server'

// Try to use the full framework service, fallback to simplified
let frameworkService: any
try {
  frameworkService = require('@/lib/framework-service').frameworkService
} catch (error) {
  console.warn('⚠️ Full framework service not available, using simplified version')
  frameworkService = require('@/lib/framework-service-simple').simplifiedFrameworkService
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')

    switch (action) {
      case 'status':
        try {
          const status = frameworkService.getStatus()
          return NextResponse.json({ success: true, data: status })
        } catch (error) {
          console.warn('Status error:', error)
          return NextResponse.json({
            success: true,
            data: { initialized: false, agentCount: 0, config: {} }
          })
        }

      case 'analytics':
        try {
          const analytics = frameworkService.getAnalytics()
          return NextResponse.json({ success: true, data: analytics })
        } catch (error) {
          console.warn('Analytics error:', error)
          return NextResponse.json({
            success: true,
            data: {
              totalProjects: 0,
              successRate: 1.0,
              averageExecutionTime: 0,
              agentUtilization: {}
            }
          })
        }

      case 'agents':
        try {
          const agents = frameworkService.getAvailableAgents()
          return NextResponse.json({ success: true, data: agents })
        } catch (error) {
          console.warn('Agents error:', error)
          return NextResponse.json({ success: true, data: [] })
        }

      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid action. Use: status, analytics, or agents'
        }, { status: 400 })
    }
  } catch (error) {
    console.error('Framework API error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, ...data } = body

    switch (action) {
      case 'plan_project':
        try {
          const planResult = await frameworkService.planProject(data)
          return NextResponse.json(planResult)
        } catch (error) {
          console.error('Plan project error:', error)
          return NextResponse.json({
            success: false,
            error: error instanceof Error ? error.message : 'Planning failed'
          })
        }

      case 'execute_task':
        try {
          const { agentType, task } = data
          const taskResult = await frameworkService.executeAgentTask(agentType, task)
          return NextResponse.json(taskResult)
        } catch (error) {
          console.error('Execute task error:', error)
          return NextResponse.json({
            success: false,
            error: error instanceof Error ? error.message : 'Task execution failed'
          })
        }

      case 'initialize':
        try {
          await frameworkService.initialize()
          return NextResponse.json({ success: true, message: 'Framework initialized' })
        } catch (error) {
          console.error('Initialize error:', error)
          return NextResponse.json({
            success: false,
            error: error instanceof Error ? error.message : 'Initialization failed'
          })
        }

      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid action. Use: plan_project, execute_task, or initialize'
        }, { status: 400 })
    }
  } catch (error) {
    console.error('Framework API error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
