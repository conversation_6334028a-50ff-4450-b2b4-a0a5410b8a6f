/**
 * AG3NT Platform - Framework API Routes
 * 
 * API endpoints for framework integration
 */

import { NextRequest, NextResponse } from 'next/server'
import { frameworkService } from '@/lib/framework-service'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')

    switch (action) {
      case 'status':
        const status = frameworkService.getStatus()
        return NextResponse.json({ success: true, data: status })

      case 'analytics':
        const analytics = frameworkService.getAnalytics()
        return NextResponse.json({ success: true, data: analytics })

      case 'agents':
        const agents = frameworkService.getAvailableAgents()
        return NextResponse.json({ success: true, data: agents })

      default:
        return NextResponse.json({ 
          success: false, 
          error: 'Invalid action. Use: status, analytics, or agents' 
        }, { status: 400 })
    }
  } catch (error) {
    console.error('Framework API error:', error)
    return NextResponse.json({ 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, ...data } = body

    switch (action) {
      case 'plan_project':
        const planResult = await frameworkService.planProject(data)
        return NextResponse.json(planResult)

      case 'execute_task':
        const { agentType, task } = data
        const taskResult = await frameworkService.executeAgentTask(agentType, task)
        return NextResponse.json(taskResult)

      case 'initialize':
        await frameworkService.initialize()
        return NextResponse.json({ success: true, message: 'Framework initialized' })

      default:
        return NextResponse.json({ 
          success: false, 
          error: 'Invalid action. Use: plan_project, execute_task, or initialize' 
        }, { status: 400 })
    }
  } catch (error) {
    console.error('Framework API error:', error)
    return NextResponse.json({ 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    }, { status: 500 })
  }
}
