{"name": "@ag3nt/framework", "version": "1.0.0", "description": "AG3NT Framework - The world's most advanced multi-agent autonomous development platform", "keywords": ["ai", "agents", "multi-agent", "autonomous", "development", "framework", "typescript", "machine-learning", "collaboration", "optimization"], "homepage": "https://ag3nt.dev", "repository": {"type": "git", "url": "https://github.com/ag3nt-dev/framework.git"}, "bugs": {"url": "https://github.com/ag3nt-dev/framework/issues"}, "license": "SEE LICENSE IN LICENSE.md", "author": {"name": "AG3NT Technologies", "email": "<EMAIL>", "url": "https://ag3nt.dev"}, "main": "dist/lib/ag3nt-framework/index.js", "module": "dist/lib/ag3nt-framework/index.esm.js", "types": "dist/lib/ag3nt-framework/index.d.ts", "bin": {"ag3nt": "dist/cli/index.js"}, "files": ["dist", "LICENSE.md", "README.md", "CHANGELOG.md", "docs"], "exports": {".": {"import": "./dist/lib/ag3nt-framework/index.esm.js", "require": "./dist/lib/ag3nt-framework/index.js", "types": "./dist/lib/ag3nt-framework/index.d.ts"}, "./core": {"import": "./dist/lib/ag3nt-framework/core/index.esm.js", "require": "./dist/lib/ag3nt-framework/core/index.js", "types": "./dist/lib/ag3nt-framework/core/index.d.ts"}, "./agents": {"import": "./dist/lib/ag3nt-framework/agents/index.esm.js", "require": "./dist/lib/ag3nt-framework/agents/index.js", "types": "./dist/lib/ag3nt-framework/agents/index.d.ts"}, "./advanced": {"import": "./dist/lib/ag3nt-framework/advanced/index.esm.js", "require": "./dist/lib/ag3nt-framework/advanced/index.js", "types": "./dist/lib/ag3nt-framework/advanced/index.d.ts"}}, "scripts": {"build": "npm run clean && npm run build:framework && npm run build:cli && npm run build:docs", "build:framework": "tsc -p tsconfig.build.json", "build:cli": "npm run build:framework && node scripts/build-cli.js", "build:docs": "typedoc lib/ag3nt-framework/index.ts --out docs/api", "clean": "<PERSON><PERSON><PERSON> dist", "dev": "next dev", "dev:framework": "tsx watch lib/ag3nt-framework/index.ts", "test": "jest", "test:watch": "jest --watch", "framework:demo": "tsx examples/ag3nt-framework-master-demo.ts", "framework:test": "npm test", "framework:setup": "npm install", "framework:test-integration": "node scripts/test-integration.js", "framework:quick-test": "node scripts/quick-test.js", "framework:test-isolated": "node test-isolated-framework.js", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "lint": "eslint lib --ext .ts", "lint:fix": "eslint lib --ext .ts --fix", "format": "prettier --write lib/**/*.{ts,json,md}", "format:check": "prettier --check lib/**/*.{ts,json,md}", "type-check": "tsc --noEmit", "docs:serve": "http-server docs -p 8080", "prepublishOnly": "npm run build && npm run test && npm run lint", "release": "semantic-release", "example:basic": "tsx examples/basic/index.ts", "example:advanced": "tsx examples/advanced/index.ts", "benchmark": "tsx benchmarks/index.ts", "security:audit": "npm audit", "deps:check": "ncu", "deps:update": "ncu -u && npm install", "next:build": "next build", "next:dev": "next dev", "next:lint": "next lint", "next:start": "next start", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "dev:full": "concurrently \"pnpm dev\" \"pnpm storybook\""}, "dependencies": {"axios": "^1.6.0", "ws": "^8.14.0", "uuid": "^9.0.0", "lodash": "^4.17.21", "eventemitter3": "^5.0.1", "dotenv": "^16.3.0", "chalk": "^5.3.0", "ora": "^7.0.1", "inquirer": "^9.2.0", "commander": "^11.1.0", "semver": "^7.5.0", "jsonschema": "^1.4.1", "yaml": "^2.3.0", "fast-json-stringify": "^5.8.0", "pino": "^8.16.0", "pino-pretty": "^10.2.0", "@emotion/is-prop-valid": "latest", "@hookform/resolvers": "^3.9.1", "@langchain/core": "latest", "@langchain/langgraph": "latest", "@langchain/openai": "latest", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@sentry/nextjs": "^8.42.0", "@t3-oss/env-nextjs": "^0.11.1", "@tanstack/react-query": "^5.59.0", "@trpc/client": "^10.45.2", "@trpc/react-query": "^10.45.2", "@trpc/server": "^10.45.2", "@types/three": "^0.178.1", "@uploadthing/react": "^7.2.0", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "4.1.0", "embla-carousel-react": "8.5.1", "framer-motion": "^11.11.17", "inngest": "^3.25.1", "input-otp": "1.4.1", "ioredis": "^5.6.1", "lucide-react": "^0.454.0", "nanoid": "^5.0.9", "neo4j-driver": "^5.28.1", "next": "15.2.4", "next-themes": "^0.4.4", "partykit": "^0.0.111", "partysocket": "^1.0.2", "posthog-js": "^1.176.0", "react": "^19", "react-day-picker": "8.10.1", "react-dom": "^19", "react-hook-form": "^7.53.2", "react-markdown": "^10.1.0", "react-resizable-panels": "^2.1.7", "recharts": "2.15.0", "remark-gfm": "^4.0.1", "sonner": "^1.7.1", "superjson": "^2.2.1", "tailwind-merge": "^2.5.5", "tailwind-scrollbar": "3.1.0", "tailwindcss-animate": "^1.0.7", "three": "^0.178.0", "uploadthing": "^7.2.0", "vaul": "^0.9.6", "zod": "^3.24.1", "zod-to-json-schema": "latest"}, "devDependencies": {"@types/jest": "^29.5.0", "@types/lodash": "^4.14.0", "@types/uuid": "^9.0.0", "@types/ws": "^8.5.0", "@types/inquirer": "^9.0.0", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "esbuild": "^0.19.0", "jest": "^29.7.0", "ts-jest": "^29.1.0", "tsx": "^4.6.0", "eslint": "^8.50.0", "prettier": "^3.0.0", "rimraf": "^5.0.0", "typedoc": "^0.25.0", "semantic-release": "^22.0.0", "conventional-changelog-cli": "^4.1.0", "npm-check-updates": "^16.14.0", "http-server": "^14.1.0", "husky": "^8.0.0", "lint-staged": "^15.0.0", "@playwright/test": "^1.49.1", "@storybook/addon-essentials": "^8.4.7", "@storybook/addon-interactions": "^8.4.7", "@storybook/addon-links": "^8.4.7", "@storybook/blocks": "^8.4.7", "@storybook/nextjs": "^8.4.7", "@storybook/react": "^8.4.7", "@storybook/test": "^8.4.7", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "@vitest/coverage-v8": "^2.1.8", "concurrently": "^9.1.0", "msw": "^2.6.8", "postcss": "^8.5", "storybook": "^8.4.7", "tailwindcss": "^3.4.17", "typescript": "^5", "vitest": "^2.1.8"}, "peerDependencies": {"node": ">=18.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "publishConfig": {"access": "public", "registry": "https://registry.npmjs.org/"}, "ag3nt": {"version": "1.0.0", "edition": "community", "features": {"core": true, "agents": true, "workflows": true, "basic_monitoring": true, "advanced_features": false, "enterprise_support": false, "commercial_license": false}, "limits": {"max_agents": 10, "max_concurrent_sessions": 5, "max_workflow_complexity": "medium"}}}