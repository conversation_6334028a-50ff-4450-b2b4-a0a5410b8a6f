/**
 * AG3NT Platform - Live Code Generation Display
 * 
 * Shows real-time code generation and file creation
 */

'use client'

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Separator } from "@/components/ui/separator"
import { 
  FileText, 
  Folder, 
  Code, 
  Database, 
  TestTube,
  Eye,
  Download,
  Copy,
  Terminal,
  Zap,
  CheckCircle
} from 'lucide-react'
import { useCoding } from '@/hooks/use-coding'

interface GeneratedFile {
  path: string
  content: string
  type: 'component' | 'api' | 'schema' | 'test' | 'config'
  size: number
  timestamp: number
}

export function LiveCodeDisplay() {
  const coding = useCoding()
  const [selectedFile, setSelectedFile] = useState<GeneratedFile | null>(null)
  const [generatedFiles, setGeneratedFiles] = useState<GeneratedFile[]>([])

  // Simulate file generation based on completed tasks
  useEffect(() => {
    if (!coding.tasks) return

    const newFiles: GeneratedFile[] = []
    
    coding.tasks.forEach(task => {
      if (task.status === 'completed' && task.output) {
        // Generate mock files based on task output
        if (task.output.files) {
          task.output.files.forEach((filePath: string) => {
            const file: GeneratedFile = {
              path: filePath,
              content: generateMockFileContent(filePath, task.type),
              type: getFileType(filePath),
              size: Math.floor(Math.random() * 5000) + 500,
              timestamp: task.endTime || Date.now()
            }
            newFiles.push(file)
          })
        }
      }
    })

    setGeneratedFiles(newFiles)
  }, [coding.tasks])

  const generateMockFileContent = (filePath: string, taskType: string): string => {
    const fileName = filePath.split('/').pop() || ''
    
    if (fileName.endsWith('.tsx') || fileName.endsWith('.jsx')) {
      return `import React from 'react'
import { useState } from 'react'

interface ${fileName.replace('.tsx', '')}Props {
  // Component props
}

export function ${fileName.replace('.tsx', '')}({ }: ${fileName.replace('.tsx', '')}Props) {
  const [state, setState] = useState()

  return (
    <div className="cyberpunk-theme">
      <h1 className="neon-glow">Cyberpunk Calculator</h1>
      {/* Component implementation */}
    </div>
  )
}

export default ${fileName.replace('.tsx', '')}`
    }
    
    if (fileName.endsWith('.ts') && filePath.includes('controller')) {
      return `import { Controller, Get, Post, Body } from '@nestjs/common'
import { CalculatorService } from './calculator.service'

@Controller('api/calculator')
export class CalculatorController {
  constructor(private readonly calculatorService: CalculatorService) {}

  @Post('calculate')
  async calculate(@Body() data: any) {
    return this.calculatorService.calculate(data)
  }

  @Get('history')
  async getHistory() {
    return this.calculatorService.getHistory()
  }
}`
    }
    
    if (fileName === 'schema.prisma') {
      return `generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id          String   @id @default(cuid())
  email       String   @unique
  calculations Calculation[]
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model Calculation {
  id        String   @id @default(cuid())
  expression String
  result     Float
  userId    String
  user      User     @relation(fields: [userId], references: [id])
  createdAt DateTime @default(now())
}`
    }
    
    if (fileName.endsWith('.test.ts') || fileName.endsWith('.spec.ts')) {
      return `import { render, screen } from '@testing-library/react'
import { Calculator } from './Calculator'

describe('Calculator Component', () => {
  test('renders calculator interface', () => {
    render(<Calculator />)
    expect(screen.getByText('Cyberpunk Calculator')).toBeInTheDocument()
  })

  test('performs basic calculations', () => {
    render(<Calculator />)
    // Test implementation
  })
})`
    }
    
    return `// Generated file: ${fileName}
// Task type: ${taskType}
// Timestamp: ${new Date().toISOString()}

export default {
  // File content generated by AG3NT Framework
}`
  }

  const getFileType = (filePath: string): GeneratedFile['type'] => {
    if (filePath.includes('component') || filePath.endsWith('.tsx')) return 'component'
    if (filePath.includes('api') || filePath.includes('controller')) return 'api'
    if (filePath.includes('schema') || filePath.includes('prisma')) return 'schema'
    if (filePath.includes('test') || filePath.includes('spec')) return 'test'
    return 'config'
  }

  const getFileIcon = (type: GeneratedFile['type']) => {
    switch (type) {
      case 'component': return <Code className="h-4 w-4 text-blue-500" />
      case 'api': return <Zap className="h-4 w-4 text-orange-500" />
      case 'schema': return <Database className="h-4 w-4 text-green-500" />
      case 'test': return <TestTube className="h-4 w-4 text-purple-500" />
      default: return <FileText className="h-4 w-4 text-gray-500" />
    }
  }

  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return `${bytes} B`
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`
  }

  const copyToClipboard = (content: string) => {
    navigator.clipboard.writeText(content)
  }

  if (!coding.isRunning && generatedFiles.length === 0) {
    return null
  }

  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Terminal className="h-5 w-5 text-green-500" />
            <CardTitle className="text-lg">Live Code Generation</CardTitle>
          </div>
          <Badge variant="outline">
            {generatedFiles.length} files generated
          </Badge>
        </div>
        <CardDescription>
          Real-time view of code being generated by AI agents
        </CardDescription>
      </CardHeader>

      <CardContent>
        <Tabs defaultValue="files" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="files">Files ({generatedFiles.length})</TabsTrigger>
            <TabsTrigger value="structure">Project Structure</TabsTrigger>
            <TabsTrigger value="preview">Code Preview</TabsTrigger>
          </TabsList>

          <TabsContent value="files" className="space-y-4">
            <ScrollArea className="h-64">
              <div className="space-y-2">
                {generatedFiles.map((file, index) => (
                  <div 
                    key={index}
                    className="flex items-center justify-between p-2 rounded-lg border hover:bg-muted/50 cursor-pointer"
                    onClick={() => setSelectedFile(file)}
                  >
                    <div className="flex items-center space-x-3">
                      {getFileIcon(file.type)}
                      <div>
                        <div className="text-sm font-medium">{file.path.split('/').pop()}</div>
                        <div className="text-xs text-muted-foreground">{file.path}</div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-xs text-muted-foreground">{formatFileSize(file.size)}</div>
                      <div className="text-xs text-green-600">
                        <CheckCircle className="h-3 w-3 inline mr-1" />
                        Generated
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </TabsContent>

          <TabsContent value="structure" className="space-y-4">
            <div className="text-sm font-mono bg-muted p-4 rounded-lg">
              <div className="space-y-1">
                <div className="flex items-center space-x-2">
                  <Folder className="h-4 w-4 text-blue-500" />
                  <span>cyberpunk-calculator/</span>
                </div>
                <div className="ml-6 space-y-1">
                  <div>📁 src/</div>
                  <div className="ml-4">📁 components/</div>
                  <div className="ml-8">📄 Calculator.tsx</div>
                  <div className="ml-8">📄 Display.tsx</div>
                  <div className="ml-8">📄 Button.tsx</div>
                  <div className="ml-4">📁 api/</div>
                  <div className="ml-8">📄 calculator.controller.ts</div>
                  <div className="ml-8">📄 calculator.service.ts</div>
                  <div className="ml-4">📁 prisma/</div>
                  <div className="ml-8">📄 schema.prisma</div>
                  <div className="ml-4">📁 tests/</div>
                  <div className="ml-8">📄 calculator.test.ts</div>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="preview" className="space-y-4">
            {selectedFile ? (
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    {getFileIcon(selectedFile.type)}
                    <span className="font-medium">{selectedFile.path}</span>
                  </div>
                  <div className="flex space-x-2">
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => copyToClipboard(selectedFile.content)}
                    >
                      <Copy className="h-4 w-4 mr-1" />
                      Copy
                    </Button>
                    <Button size="sm" variant="outline">
                      <Download className="h-4 w-4 mr-1" />
                      Download
                    </Button>
                  </div>
                </div>
                
                <ScrollArea className="h-64">
                  <pre className="text-xs bg-muted p-4 rounded-lg overflow-x-auto">
                    <code>{selectedFile.content}</code>
                  </pre>
                </ScrollArea>
              </div>
            ) : (
              <div className="text-center text-muted-foreground py-8">
                <Eye className="h-8 w-8 mx-auto mb-2" />
                <p>Select a file to preview its content</p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}

export default LiveCodeDisplay
