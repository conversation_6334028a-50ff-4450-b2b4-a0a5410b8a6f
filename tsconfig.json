{"compilerOptions": {"lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "target": "ES6", "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./*"], "ag3nt-framework": ["./ag3nt-framework-standalone/src"], "ag3nt-framework/*": ["./ag3nt-framework-standalone/src/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}