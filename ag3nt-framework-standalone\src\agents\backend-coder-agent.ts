/**
 * AG3NT Framework - Backend Coder Agent
 * 
 * Specialized agent for backend development tasks.
 * Handles API development, database design, server architecture, and backend services.
 * 
 * Features:
 * - REST/GraphQL API development
 * - Database design and optimization
 * - Microservices architecture
 * - Authentication and authorization
 * - Performance optimization
 * - Security implementation
 * - Testing and documentation
 */

import { BaseAgent, AgentState, AgentConfig } from "../core/base-agent"
import { aiService } from "../../ai-service"

export interface BackendCoderInput {
  task: BackendTask
  architecture: BackendArchitecture
  requirements: BackendRequirements
  codebase: BackendCodebaseContext
}

export interface BackendTask {
  taskId: string
  type: 'api' | 'database' | 'service' | 'integration' | 'security' | 'optimization' | 'testing'
  title: string
  description: string
  priority: 'critical' | 'high' | 'medium' | 'low'
  acceptanceCriteria: string[]
  technicalRequirements: string[]
  dependencies: string[]
}

export interface BackendArchitecture {
  pattern: 'monolith' | 'microservices' | 'serverless' | 'hybrid'
  apiStyle: 'rest' | 'graphql' | 'grpc' | 'websocket'
  database: DatabaseArchitecture
  caching: CachingStrategy
  messaging: MessagingArchitecture
  deployment: DeploymentArchitecture
}

export interface DatabaseArchitecture {
  type: 'sql' | 'nosql' | 'hybrid'
  primary: DatabaseConfig
  secondary?: DatabaseConfig[]
  migrations: boolean
  seeding: boolean
  backup: BackupStrategy
}

export interface DatabaseConfig {
  engine: 'postgresql' | 'mysql' | 'mongodb' | 'redis' | 'elasticsearch'
  version: string
  host: string
  port: number
  credentials: DatabaseCredentials
  pooling: ConnectionPooling
}

export interface DatabaseCredentials {
  username: string
  password: string
  database: string
  ssl: boolean
}

export interface ConnectionPooling {
  min: number
  max: number
  idle: number
  acquire: number
}

export interface BackupStrategy {
  frequency: 'hourly' | 'daily' | 'weekly'
  retention: number
  location: 'local' | 'cloud' | 'both'
  encryption: boolean
}

export interface CachingStrategy {
  levels: CacheLevel[]
  invalidation: 'ttl' | 'manual' | 'event-driven'
  distribution: 'local' | 'distributed'
}

export interface CacheLevel {
  name: string
  type: 'memory' | 'redis' | 'memcached'
  ttl: number
  maxSize: number
}

export interface MessagingArchitecture {
  pattern: 'pub-sub' | 'queue' | 'stream' | 'hybrid'
  broker: 'rabbitmq' | 'kafka' | 'redis' | 'aws-sqs' | 'none'
  topics: MessageTopic[]
}

export interface MessageTopic {
  name: string
  type: 'event' | 'command' | 'query'
  schema: any
  retention: number
}

export interface DeploymentArchitecture {
  platform: 'docker' | 'kubernetes' | 'serverless' | 'vm'
  environment: 'development' | 'staging' | 'production'
  scaling: ScalingStrategy
  monitoring: MonitoringStrategy
}

export interface ScalingStrategy {
  type: 'horizontal' | 'vertical' | 'auto'
  minInstances: number
  maxInstances: number
  metrics: string[]
}

export interface MonitoringStrategy {
  logging: LoggingConfig
  metrics: MetricsConfig
  tracing: TracingConfig
  alerting: AlertingConfig
}

export interface LoggingConfig {
  level: 'debug' | 'info' | 'warn' | 'error'
  format: 'json' | 'text'
  destination: 'console' | 'file' | 'cloud'
}

export interface MetricsConfig {
  enabled: boolean
  endpoint: string
  interval: number
  retention: number
}

export interface TracingConfig {
  enabled: boolean
  sampler: number
  exporter: string
}

export interface AlertingConfig {
  enabled: boolean
  channels: string[]
  thresholds: Record<string, number>
}

export interface BackendRequirements {
  language: 'typescript' | 'javascript' | 'python' | 'java' | 'go' | 'rust' | 'csharp'
  framework: string
  runtime: string
  dependencies: Dependency[]
  security: SecurityRequirements
  performance: BackendPerformanceRequirements
  compliance: ComplianceRequirements
}

export interface Dependency {
  name: string
  version: string
  type: 'runtime' | 'development' | 'peer'
  purpose: string
}

export interface SecurityRequirements {
  authentication: AuthenticationConfig
  authorization: AuthorizationConfig
  encryption: EncryptionConfig
  validation: ValidationConfig
  rateLimit: RateLimitConfig
}

export interface AuthenticationConfig {
  method: 'jwt' | 'oauth' | 'saml' | 'basic' | 'api-key'
  provider: string
  expiration: number
  refresh: boolean
}

export interface AuthorizationConfig {
  model: 'rbac' | 'abac' | 'acl'
  roles: Role[]
  permissions: Permission[]
}

export interface Role {
  name: string
  description: string
  permissions: string[]
}

export interface Permission {
  name: string
  resource: string
  actions: string[]
}

export interface EncryptionConfig {
  atRest: boolean
  inTransit: boolean
  algorithm: string
  keyManagement: string
}

export interface ValidationConfig {
  input: boolean
  output: boolean
  schema: string
  sanitization: boolean
}

export interface RateLimitConfig {
  enabled: boolean
  window: number
  max: number
  strategy: 'fixed' | 'sliding' | 'token-bucket'
}

export interface BackendPerformanceRequirements {
  responseTime: number // in ms
  throughput: number // requests per second
  concurrency: number // concurrent connections
  memory: number // in MB
  cpu: number // percentage
}

export interface ComplianceRequirements {
  standards: string[] // GDPR, HIPAA, SOC2, etc.
  auditing: boolean
  dataRetention: number // in days
  dataLocality: string[]
}

export interface BackendCodebaseContext {
  projectStructure: BackendProjectStructure
  existingServices: ExistingService[]
  apis: ExistingAPI[]
  database: ExistingDatabase
  dependencies: Dependency[]
  configuration: ConfigurationFiles
}

export interface BackendProjectStructure {
  srcDirectory: string
  controllersDirectory: string
  servicesDirectory: string
  modelsDirectory: string
  middlewareDirectory: string
  testsDirectory: string
  configDirectory: string
}

export interface ExistingService {
  name: string
  path: string
  type: 'controller' | 'service' | 'middleware' | 'model' | 'utility'
  dependencies: string[]
  exports: string[]
}

export interface ExistingAPI {
  path: string
  method: string
  handler: string
  middleware: string[]
  parameters: APIParameter[]
  responses: APIResponse[]
}

export interface APIParameter {
  name: string
  type: string
  location: 'path' | 'query' | 'body' | 'header'
  required: boolean
  validation: string
}

export interface APIResponse {
  status: number
  description: string
  schema: any
  examples: any[]
}

export interface ExistingDatabase {
  tables: DatabaseTable[]
  relationships: DatabaseRelationship[]
  indexes: DatabaseIndex[]
  constraints: DatabaseConstraint[]
}

export interface DatabaseTable {
  name: string
  columns: DatabaseColumn[]
  primaryKey: string[]
  timestamps: boolean
}

export interface DatabaseColumn {
  name: string
  type: string
  nullable: boolean
  default?: any
  unique: boolean
}

export interface DatabaseRelationship {
  type: 'one-to-one' | 'one-to-many' | 'many-to-many'
  from: string
  to: string
  foreignKey: string
  references: string
}

export interface DatabaseIndex {
  name: string
  table: string
  columns: string[]
  unique: boolean
  type: 'btree' | 'hash' | 'gin' | 'gist'
}

export interface DatabaseConstraint {
  name: string
  table: string
  type: 'check' | 'unique' | 'foreign_key'
  definition: string
}

export interface ConfigurationFiles {
  environment: Record<string, any>
  database: Record<string, any>
  server: Record<string, any>
  security: Record<string, any>
}

export interface BackendCoderResult {
  taskId: string
  status: 'completed' | 'failed' | 'needs_review'
  deliverables: BackendDeliverable[]
  codeChanges: BackendCodeChange[]
  databaseChanges: DatabaseChange[]
  apiChanges: APIChange[]
  testResults: BackendTestResult[]
  performanceMetrics: BackendPerformanceMetrics
  securityReport: SecurityReport
  documentation: BackendDocumentation[]
}

export interface BackendDeliverable {
  type: 'controller' | 'service' | 'model' | 'middleware' | 'migration' | 'test' | 'config'
  name: string
  path: string
  content: string
  dependencies: string[]
}

export interface BackendCodeChange {
  file: string
  type: 'create' | 'modify' | 'delete'
  changes: string
  linesAdded: number
  linesRemoved: number
  impact: 'low' | 'medium' | 'high'
}

export interface DatabaseChange {
  type: 'create_table' | 'alter_table' | 'drop_table' | 'create_index' | 'drop_index'
  table: string
  migration: string
  rollback: string
}

export interface APIChange {
  endpoint: string
  method: string
  type: 'create' | 'modify' | 'deprecate' | 'remove'
  breaking: boolean
  documentation: string
}

export interface BackendTestResult {
  testFile: string
  testSuite: string
  passed: number
  failed: number
  coverage: number
  duration: number
  type: 'unit' | 'integration' | 'e2e'
}

export interface BackendPerformanceMetrics {
  responseTime: number
  throughput: number
  memoryUsage: number
  cpuUsage: number
  databaseQueries: number
  cacheHitRate: number
}

export interface SecurityReport {
  vulnerabilities: SecurityVulnerability[]
  compliance: ComplianceCheck[]
  recommendations: string[]
  score: number
}

export interface SecurityVulnerability {
  type: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  description: string
  location: string
  fix: string
}

export interface ComplianceCheck {
  standard: string
  requirement: string
  status: 'compliant' | 'non-compliant' | 'partial'
  evidence: string
}

export interface BackendDocumentation {
  type: 'api' | 'service' | 'database' | 'deployment' | 'security'
  title: string
  content: string
  examples: any[]
  changelog: string[]
}

/**
 * Backend Coder Agent - Specialized backend development
 */
export class BackendCoderAgent extends BaseAgent {
  private readonly codingSteps = [
    'analyze_requirements', 'design_architecture', 'setup_environment',
    'implement_apis', 'design_database', 'implement_services',
    'add_security', 'optimize_performance', 'write_tests', 'document_apis'
  ]

  constructor(config: Partial<AgentConfig> = {}) {
    super('backend-coder', {
      capabilities: {
        requiredCapabilities: [
          'backend_development',
          'api_design',
          'database_design',
          'security_implementation',
          'performance_optimization',
          'microservices_architecture',
          'backend_testing'
        ],
        contextFilters: ['backend', 'api', 'database', 'security', 'code'],
        mcpEnhanced: true,
        sequentialThinking: true,
        contextEnrichment: true,
        ragIntegration: true
      },
      ...config
    })
  }

  /**
   * Execute backend coding workflow
   */
  protected async executeWorkflow(state: AgentState): Promise<AgentState> {
    const input = state.input as BackendCoderInput
    
    console.log(`⚙️ Starting backend development: ${input.task.title}`)

    // Execute coding steps sequentially
    for (const stepId of this.codingSteps) {
      console.log(`🔄 Executing step: ${stepId}`)
      
      const stepResult = await this.executeStepWithContext(stepId, input)
      
      if (stepResult.needsInput) {
        state.needsInput = true
        state.question = stepResult.question
        break
      }

      if (stepResult.needsReview) {
        state.results.status = 'needs_review'
        state.results.reviewReason = stepResult.reviewReason
        break
      }

      // Update progress
      state.metadata.completedSteps++
      state.metadata.lastUpdated = new Date().toISOString()
      
      this.emit('step_completed', {
        agentId: this.agentId,
        stepId,
        progress: state.metadata.completedSteps / state.metadata.totalSteps
      })
    }

    // Mark as completed if no input needed and no review required
    if (!state.needsInput && state.results.status !== 'needs_review') {
      state.completed = true
      console.log(`✅ Backend development completed: ${input.task.title}`)
    }

    return state
  }

  /**
   * Execute individual coding step with context enhancement
   */
  private async executeStepWithContext(stepId: string, input?: any): Promise<any> {
    // Get enhanced context from context engine
    const enhancedState = await this.getEnhancedContext(stepId)

    // Execute step with MCP enhancement
    switch (stepId) {
      case 'analyze_requirements':
        return await this.analyzeRequirementsWithMCP(enhancedState, input)
      case 'design_architecture':
        return await this.designArchitectureWithMCP(enhancedState)
      case 'setup_environment':
        return await this.setupEnvironmentWithMCP(enhancedState)
      case 'implement_apis':
        return await this.implementAPIsWithMCP(enhancedState)
      case 'design_database':
        return await this.designDatabaseWithMCP(enhancedState)
      case 'implement_services':
        return await this.implementServicesWithMCP(enhancedState)
      case 'add_security':
        return await this.addSecurityWithMCP(enhancedState)
      case 'optimize_performance':
        return await this.optimizePerformanceWithMCP(enhancedState)
      case 'write_tests':
        return await this.writeTestsWithMCP(enhancedState)
      case 'document_apis':
        return await this.documentAPIsWithMCP(enhancedState)
      default:
        throw new Error(`Unknown backend coding step: ${stepId}`)
    }
  }

  /**
   * Get total steps for progress tracking
   */
  protected getTotalSteps(): number {
    return this.codingSteps.length
  }

  /**
   * Get relevant documentation for backend development
   */
  protected async getRelevantDocumentation(): Promise<Record<string, any>> {
    return {
      backendDevelopment: 'Modern backend development practices and patterns',
      apiDesign: 'RESTful API design principles and GraphQL best practices',
      databaseDesign: 'Database design patterns and optimization techniques',
      security: 'Backend security implementation and best practices',
      performance: 'Backend performance optimization and scaling strategies',
      testing: 'Backend testing strategies and test-driven development'
    }
  }

  // ============================================================================
  // STEP IMPLEMENTATIONS WITH MCP ENHANCEMENT
  // ============================================================================

  private async analyzeRequirementsWithMCP(state: any, input: BackendCoderInput): Promise<any> {
    const analysis = await aiService.analyzeBackendRequirements(
      input.task,
      input.architecture,
      input.requirements,
      input.codebase
    )

    this.state!.results.requirementsAnalysis = analysis
    
    return {
      results: analysis,
      needsInput: false,
      completed: false
    }
  }

  private async designArchitectureWithMCP(state: any): Promise<any> {
    const requirementsAnalysis = this.state!.results.requirementsAnalysis
    
    const architectureDesign = await aiService.designBackendArchitecture(
      requirementsAnalysis,
      this.state!.input.architecture
    )

    this.state!.results.architectureDesign = architectureDesign
    
    return {
      results: architectureDesign,
      needsInput: false,
      completed: false
    }
  }

  private async setupEnvironmentWithMCP(state: any): Promise<any> {
    const architectureDesign = this.state!.results.architectureDesign
    
    const environmentSetup = await aiService.setupBackendEnvironment(
      architectureDesign,
      this.state!.input.requirements
    )

    this.state!.results.environmentSetup = environmentSetup
    
    return {
      results: environmentSetup,
      needsInput: false,
      completed: false
    }
  }

  private async implementAPIsWithMCP(state: any): Promise<any> {
    const architectureDesign = this.state!.results.architectureDesign
    
    const apis = await aiService.implementBackendAPIs(
      architectureDesign,
      this.state!.input.requirements
    )

    this.state!.results.apis = apis
    
    return {
      results: apis,
      needsInput: false,
      completed: false
    }
  }

  private async designDatabaseWithMCP(state: any): Promise<any> {
    const architectureDesign = this.state!.results.architectureDesign
    
    const database = await aiService.designBackendDatabase(
      architectureDesign,
      this.state!.input.architecture.database
    )

    this.state!.results.database = database
    
    return {
      results: database,
      needsInput: false,
      completed: false
    }
  }

  private async implementServicesWithMCP(state: any): Promise<any> {
    const apis = this.state!.results.apis
    const database = this.state!.results.database
    
    const services = await aiService.implementBackendServices(
      apis,
      database,
      this.state!.input.requirements
    )

    this.state!.results.services = services
    
    return {
      results: services,
      needsInput: false,
      completed: false
    }
  }

  private async addSecurityWithMCP(state: any): Promise<any> {
    const services = this.state!.results.services
    
    const security = await aiService.addBackendSecurity(
      services,
      this.state!.input.requirements.security
    )

    this.state!.results.security = security
    
    return {
      results: security,
      needsInput: false,
      completed: false
    }
  }

  private async optimizePerformanceWithMCP(state: any): Promise<any> {
    const services = this.state!.results.services
    
    const optimization = await aiService.optimizeBackendPerformance(
      services,
      this.state!.input.requirements.performance
    )

    this.state!.results.optimization = optimization
    
    return {
      results: optimization,
      needsInput: false,
      completed: false
    }
  }

  private async writeTestsWithMCP(state: any): Promise<any> {
    const services = this.state!.results.services
    const apis = this.state!.results.apis
    
    const tests = await aiService.writeBackendTests(
      services,
      apis,
      this.state!.input.requirements
    )

    this.state!.results.tests = tests
    
    return {
      results: tests,
      needsInput: false,
      completed: false
    }
  }

  private async documentAPIsWithMCP(state: any): Promise<any> {
    const apis = this.state!.results.apis
    const services = this.state!.results.services
    
    const documentation = await aiService.documentBackendAPIs(
      apis,
      services,
      this.state!.input.task
    )

    this.state!.results.documentation = documentation
    
    return {
      results: documentation,
      needsInput: false,
      completed: true // Final step
    }
  }
}

// Export for easy access
export { BackendCoderAgent as default }
