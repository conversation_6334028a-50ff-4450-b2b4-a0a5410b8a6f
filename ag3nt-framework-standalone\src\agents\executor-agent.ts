/**
 * AG3NT Framework - Executor Agent
 * 
 * Specialized agent for executing individual tasks and coordinating with other agents.
 * Acts as the orchestrator for task execution and delegation.
 * 
 * Features:
 * - Task execution coordination
 * - Agent delegation and handoffs
 * - Progress monitoring and reporting
 * - Error handling and recovery
 * - Quality assurance integration
 */

import { BaseAgent, AgentState, AgentConfig } from "../core/base-agent"
import { aiService } from "../../ai-service"

export interface ExecutorInput {
  task: ExecutableTask
  context: ExecutionContext
  resources: AvailableResources
}

export interface ExecutableTask {
  taskId: string
  title: string
  description: string
  type: 'development' | 'design' | 'testing' | 'deployment' | 'documentation'
  priority: 'critical' | 'high' | 'medium' | 'low'
  requirements: string[]
  acceptanceCriteria: string[]
  dependencies: string[]
  estimatedHours: number
  assignedAgent?: string
  deadline?: string
}

export interface ExecutionContext {
  projectId: string
  sessionId: string
  previousResults: Record<string, any>
  availableAgents: string[]
  constraints: ExecutionConstraints
}

export interface ExecutionConstraints {
  timeLimit?: number
  qualityThreshold?: number
  resourceLimits?: Record<string, number>
  complianceRequirements?: string[]
}

export interface AvailableResources {
  agents: AgentResource[]
  tools: ToolResource[]
  data: DataResource[]
}

export interface AgentResource {
  agentId: string
  agentType: string
  capabilities: string[]
  availability: number // 0-1
  currentLoad: number // 0-1
}

export interface ToolResource {
  toolId: string
  toolType: string
  capabilities: string[]
  available: boolean
}

export interface DataResource {
  resourceId: string
  resourceType: string
  location: string
  accessible: boolean
}

export interface ExecutorResult {
  taskId: string
  status: 'completed' | 'failed' | 'delegated' | 'in_progress'
  results: any
  executionLog: ExecutionLogEntry[]
  qualityMetrics: QualityMetrics
  resourceUsage: ResourceUsage
  nextActions: NextAction[]
}

export interface ExecutionLogEntry {
  timestamp: string
  action: string
  agentId: string
  details: any
  status: 'success' | 'error' | 'warning' | 'info'
}

export interface QualityMetrics {
  completeness: number // 0-1
  accuracy: number // 0-1
  performance: number // 0-1
  maintainability: number // 0-1
  overallScore: number // 0-1
}

export interface ResourceUsage {
  timeSpent: number // in minutes
  agentsUsed: string[]
  toolsUsed: string[]
  costEstimate: number
}

export interface NextAction {
  actionType: 'delegate' | 'review' | 'test' | 'deploy' | 'document'
  targetAgent: string
  priority: number
  description: string
}

/**
 * Executor Agent - Task execution and coordination
 */
export class ExecutorAgent extends BaseAgent {
  private readonly executionSteps = [
    'analyze_task', 'plan_execution', 'delegate_or_execute', 
    'monitor_progress', 'quality_check', 'handle_results', 'plan_next_actions'
  ]

  constructor(config: Partial<AgentConfig> = {}) {
    super('executor', {
      capabilities: {
        requiredCapabilities: [
          'task_execution',
          'agent_coordination',
          'progress_monitoring',
          'quality_assurance',
          'error_handling',
          'resource_management'
        ],
        contextFilters: ['execution', 'tasks', 'agents', 'resources'],
        mcpEnhanced: true,
        sequentialThinking: true,
        contextEnrichment: true,
        ragIntegration: true
      },
      ...config
    })
  }

  /**
   * Execute task execution workflow
   */
  protected async executeWorkflow(state: AgentState): Promise<AgentState> {
    const input = state.input as ExecutorInput
    
    console.log(`⚡ Starting task execution: ${input.task.title}`)

    // Initialize execution log
    state.results.executionLog = []
    
    // Execute execution steps sequentially
    for (const stepId of this.executionSteps) {
      console.log(`🔄 Executing step: ${stepId}`)
      
      const stepResult = await this.executeStepWithContext(stepId, input)
      
      // Log execution step
      this.logExecution(stepId, 'info', stepResult)
      
      if (stepResult.needsInput) {
        state.needsInput = true
        state.question = stepResult.question
        break
      }

      if (stepResult.delegated) {
        state.results.status = 'delegated'
        state.results.delegatedTo = stepResult.delegatedTo
        break
      }

      // Update progress
      state.metadata.completedSteps++
      state.metadata.lastUpdated = new Date().toISOString()
      
      this.emit('step_completed', {
        agentId: this.agentId,
        stepId,
        progress: state.metadata.completedSteps / state.metadata.totalSteps
      })
    }

    // Mark as completed if no input needed and not delegated
    if (!state.needsInput && state.results.status !== 'delegated') {
      state.completed = true
      console.log(`✅ Task execution completed: ${input.task.title}`)
    }

    return state
  }

  /**
   * Execute individual execution step with context enhancement
   */
  private async executeStepWithContext(stepId: string, input?: any): Promise<any> {
    // Get enhanced context from context engine
    const enhancedState = await this.getEnhancedContext(stepId)

    // Execute step with MCP enhancement
    switch (stepId) {
      case 'analyze_task':
        return await this.analyzeTaskWithMCP(enhancedState, input)
      case 'plan_execution':
        return await this.planExecutionWithMCP(enhancedState)
      case 'delegate_or_execute':
        return await this.delegateOrExecuteWithMCP(enhancedState)
      case 'monitor_progress':
        return await this.monitorProgressWithMCP(enhancedState)
      case 'quality_check':
        return await this.qualityCheckWithMCP(enhancedState)
      case 'handle_results':
        return await this.handleResultsWithMCP(enhancedState)
      case 'plan_next_actions':
        return await this.planNextActionsWithMCP(enhancedState)
      default:
        throw new Error(`Unknown execution step: ${stepId}`)
    }
  }

  /**
   * Get total steps for progress tracking
   */
  protected getTotalSteps(): number {
    return this.executionSteps.length
  }

  /**
   * Get relevant documentation for task execution
   */
  protected async getRelevantDocumentation(): Promise<Record<string, any>> {
    return {
      taskExecution: 'Task execution patterns and best practices',
      agentCoordination: 'Multi-agent coordination and delegation strategies',
      qualityAssurance: 'Quality metrics and validation procedures',
      errorHandling: 'Error recovery and resilience patterns',
      resourceManagement: 'Resource allocation and optimization'
    }
  }

  /**
   * Log execution activity
   */
  private logExecution(action: string, status: 'success' | 'error' | 'warning' | 'info', details: any): void {
    const logEntry: ExecutionLogEntry = {
      timestamp: new Date().toISOString(),
      action,
      agentId: this.agentId,
      details,
      status
    }
    
    if (!this.state!.results.executionLog) {
      this.state!.results.executionLog = []
    }
    
    this.state!.results.executionLog.push(logEntry)
  }

  // ============================================================================
  // STEP IMPLEMENTATIONS WITH MCP ENHANCEMENT
  // ============================================================================

  private async analyzeTaskWithMCP(state: any, input: ExecutorInput): Promise<any> {
    const analysis = await aiService.analyzeExecutableTask(
      input.task,
      input.context,
      input.resources
    )

    this.state!.results.taskAnalysis = analysis
    
    return {
      results: analysis,
      needsInput: false,
      completed: false
    }
  }

  private async planExecutionWithMCP(state: any): Promise<any> {
    const taskAnalysis = this.state!.results.taskAnalysis
    
    const executionPlan = await aiService.planTaskExecution(
      taskAnalysis,
      this.state!.input.resources
    )

    this.state!.results.executionPlan = executionPlan
    
    return {
      results: executionPlan,
      needsInput: false,
      completed: false
    }
  }

  private async delegateOrExecuteWithMCP(state: any): Promise<any> {
    const executionPlan = this.state!.results.executionPlan
    const task = this.state!.input.task
    
    // Determine if task should be delegated or executed directly
    const shouldDelegate = await aiService.shouldDelegateTask(
      task,
      executionPlan,
      this.state!.input.resources.agents
    )

    if (shouldDelegate.delegate) {
      // Delegate to specialized agent
      const delegationResult = await this.delegateTask(
        task,
        shouldDelegate.targetAgent,
        shouldDelegate.delegationContext
      )
      
      return {
        results: delegationResult,
        delegated: true,
        delegatedTo: shouldDelegate.targetAgent,
        needsInput: false,
        completed: false
      }
    } else {
      // Execute directly
      const executionResult = await aiService.executeTask(task, executionPlan)
      
      this.state!.results.executionResult = executionResult
      
      return {
        results: executionResult,
        needsInput: false,
        completed: false
      }
    }
  }

  private async monitorProgressWithMCP(state: any): Promise<any> {
    const executionResult = this.state!.results.executionResult
    
    if (!executionResult) {
      // Task was delegated, monitor delegation
      const delegationStatus = await this.monitorDelegation()
      
      return {
        results: delegationStatus,
        needsInput: false,
        completed: false
      }
    }
    
    // Monitor direct execution
    const progressMetrics = await aiService.monitorTaskProgress(executionResult)

    this.state!.results.progressMetrics = progressMetrics
    
    return {
      results: progressMetrics,
      needsInput: false,
      completed: false
    }
  }

  private async qualityCheckWithMCP(state: any): Promise<any> {
    const executionResult = this.state!.results.executionResult
    
    const qualityMetrics = await aiService.performQualityCheck(
      executionResult,
      this.state!.input.task.acceptanceCriteria
    )

    this.state!.results.qualityMetrics = qualityMetrics
    
    return {
      results: qualityMetrics,
      needsInput: false,
      completed: false
    }
  }

  private async handleResultsWithMCP(state: any): Promise<any> {
    const executionResult = this.state!.results.executionResult
    const qualityMetrics = this.state!.results.qualityMetrics
    
    const resultHandling = await aiService.handleTaskResults(
      executionResult,
      qualityMetrics,
      this.state!.input.task
    )

    this.state!.results.resultHandling = resultHandling
    
    return {
      results: resultHandling,
      needsInput: false,
      completed: false
    }
  }

  private async planNextActionsWithMCP(state: any): Promise<any> {
    const resultHandling = this.state!.results.resultHandling
    const task = this.state!.input.task
    
    const nextActions = await aiService.planNextActions(
      resultHandling,
      task,
      this.state!.input.context
    )

    this.state!.results.nextActions = nextActions
    
    return {
      results: nextActions,
      needsInput: false,
      completed: true // Final step
    }
  }

  /**
   * Delegate task to specialized agent
   */
  private async delegateTask(task: ExecutableTask, targetAgent: string, context: any): Promise<any> {
    // Send delegation message through communication protocol
    // This would integrate with the AgentCommunicationProtocol
    
    console.log(`🔄 Delegating task ${task.taskId} to ${targetAgent}`)
    
    return {
      delegated: true,
      targetAgent,
      delegationId: `delegation-${Date.now()}`,
      context
    }
  }

  /**
   * Monitor delegated task progress
   */
  private async monitorDelegation(): Promise<any> {
    // Monitor delegation through communication protocol
    // This would check status of delegated tasks
    
    return {
      status: 'monitoring',
      lastUpdate: new Date().toISOString()
    }
  }
}

// Export for easy access
export { ExecutorAgent as default }
