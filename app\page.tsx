import { PlanningAgent } from "@/components/planning-agent"
import { FrameworkStatus } from "@/components/framework-status"
import { CodingProgress } from "@/components/coding-progress"
import { LiveCodeDisplay } from "@/components/live-code-display"
import { TaskProgressAnimation } from "@/components/task-progress-animation"

export default function Home() {
  return (
    <div className="min-h-screen bg-background">
      {/* Framework Status - Top right */}
      <div className="hidden lg:block fixed top-4 right-4 w-80 z-50">
        <FrameworkStatus />
      </div>

      {/* Task Progress Animation - Top left */}
      <div className="hidden lg:block fixed top-4 left-4 w-96 z-50">
        <TaskProgressAnimation />
      </div>

      {/* Live Code Display - Bottom left */}
      <div className="hidden lg:block fixed bottom-4 left-4 w-96 z-50">
        <LiveCodeDisplay />
      </div>

      {/* Coding Progress Summary - Bottom right */}
      <div className="hidden lg:block fixed bottom-4 right-4 w-80 z-50">
        <CodingProgress />
      </div>

      {/* Main Planning Agent */}
      <PlanningAgent />
    </div>
  )
}
