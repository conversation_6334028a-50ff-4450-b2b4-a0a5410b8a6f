/**
 * Jest Test Setup
 */

// Increase timeout for integration tests
jest.setTimeout(30000)

// Mock console methods to reduce noise in tests
const originalConsoleLog = console.log
const originalConsoleWarn = console.warn
const originalConsoleError = console.error

beforeAll(() => {
  console.log = jest.fn()
  console.warn = jest.fn()
  console.error = jest.fn()
})

afterAll(() => {
  console.log = originalConsoleLog
  console.warn = originalConsoleWarn
  console.error = originalConsoleError
})

// Global test utilities
global.testUtils = {
  delay: (ms: number) => new Promise(resolve => setTimeout(resolve, ms)),
  
  createMockAgent: (id: string, type: string) => ({
    id,
    type,
    capabilities: [`${type}_capability`],
    config: {
      capabilities: {
        requiredCapabilities: [`${type}_capability`]
      }
    }
  })
}
