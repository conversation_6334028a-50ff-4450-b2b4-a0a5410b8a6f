/**
 * AG3NT Framework - Security Agent
 * 
 * Specialized agent for security scanning, vulnerability assessment,
 * and secure coding compliance.
 * 
 * Features:
 * - Vulnerability scanning and assessment
 * - Security code analysis
 * - Compliance checking
 * - Threat modeling
 * - Security policy enforcement
 * - Penetration testing automation
 */

import { BaseAgent, AgentState, AgentConfig } from "../core/base-agent"
import { aiService } from "../../ai-service"

export interface SecurityInput {
  task: SecurityTask
  codebase: SecurityCodebase
  infrastructure: SecurityInfrastructure
  requirements: SecurityRequirements
}

export interface SecurityTask {
  taskId: string
  type: 'vulnerability_scan' | 'code_analysis' | 'compliance_check' | 'threat_model' | 'penetration_test' | 'audit'
  title: string
  description: string
  priority: 'critical' | 'high' | 'medium' | 'low'
  scope: SecurityScope
  deadline?: string
}

export interface SecurityScope {
  components: string[]
  layers: string[]
  environments: string[]
  standards: string[]
  includeInfrastructure: boolean
  includeApplication: boolean
  includeData: boolean
}

export interface SecurityCodebase {
  structure: CodebaseStructure
  dependencies: SecurityDependency[]
  configurations: SecurityConfiguration[]
  secrets: SecretManagement
  authentication: AuthenticationSystem
  authorization: AuthorizationSystem
}

export interface CodebaseStructure {
  files: SecurityFile[]
  modules: SecurityModule[]
  apis: SecurityAPI[]
  databases: SecurityDatabase[]
}

export interface SecurityFile {
  path: string
  type: string
  language: string
  size: number
  permissions: string
  lastModified: string
  checksum: string
  sensitive: boolean
}

export interface SecurityModule {
  name: string
  path: string
  functions: SecurityFunction[]
  classes: SecurityClass[]
  imports: string[]
  exports: string[]
  riskLevel: 'low' | 'medium' | 'high' | 'critical'
}

export interface SecurityFunction {
  name: string
  parameters: SecurityParameter[]
  returnType: string
  permissions: string[]
  validation: ValidationRule[]
  sanitization: SanitizationRule[]
}

export interface SecurityClass {
  name: string
  methods: SecurityMethod[]
  properties: SecurityProperty[]
  inheritance: string[]
  permissions: string[]
}

export interface SecurityMethod {
  name: string
  visibility: 'public' | 'private' | 'protected'
  parameters: SecurityParameter[]
  permissions: string[]
  validation: ValidationRule[]
}

export interface SecurityProperty {
  name: string
  type: string
  visibility: 'public' | 'private' | 'protected'
  sensitive: boolean
  encrypted: boolean
}

export interface SecurityParameter {
  name: string
  type: string
  validation: ValidationRule[]
  sanitization: SanitizationRule[]
  sensitive: boolean
}

export interface ValidationRule {
  type: string
  rule: string
  message: string
  severity: 'error' | 'warning' | 'info'
}

export interface SanitizationRule {
  type: string
  rule: string
  description: string
}

export interface SecurityAPI {
  endpoint: string
  method: string
  authentication: boolean
  authorization: string[]
  rateLimit: RateLimit
  validation: ValidationRule[]
  encryption: boolean
}

export interface RateLimit {
  requests: number
  window: number
  strategy: 'fixed' | 'sliding' | 'token_bucket'
}

export interface SecurityDatabase {
  name: string
  type: string
  encryption: EncryptionConfig
  access: AccessControl
  backup: BackupSecurity
  audit: AuditConfig
}

export interface EncryptionConfig {
  atRest: boolean
  inTransit: boolean
  algorithm: string
  keyManagement: string
  keyRotation: boolean
}

export interface AccessControl {
  authentication: boolean
  authorization: string[]
  roles: string[]
  permissions: string[]
  ipWhitelist: string[]
}

export interface BackupSecurity {
  encrypted: boolean
  location: string
  retention: number
  access: string[]
}

export interface AuditConfig {
  enabled: boolean
  events: string[]
  retention: number
  alerting: boolean
}

export interface SecurityDependency {
  name: string
  version: string
  type: 'direct' | 'transitive'
  vulnerabilities: Vulnerability[]
  license: LicenseInfo
  riskScore: number
}

export interface Vulnerability {
  id: string
  cve?: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  score: number
  description: string
  impact: string
  exploitability: string
  fix: VulnerabilityFix
  references: string[]
}

export interface VulnerabilityFix {
  available: boolean
  version?: string
  patch?: string
  workaround?: string
  effort: 'low' | 'medium' | 'high'
}

export interface LicenseInfo {
  name: string
  type: string
  compatible: boolean
  restrictions: string[]
  obligations: string[]
}

export interface SecurityConfiguration {
  file: string
  type: 'security_policy' | 'access_control' | 'encryption' | 'authentication'
  settings: SecuritySetting[]
  compliance: ComplianceCheck[]
}

export interface SecuritySetting {
  key: string
  value: any
  secure: boolean
  description: string
  recommendation: string
}

export interface ComplianceCheck {
  standard: string
  requirement: string
  status: 'compliant' | 'non_compliant' | 'partial' | 'unknown'
  evidence: string
  remediation: string
}

export interface SecretManagement {
  secrets: SecretInfo[]
  storage: SecretStorage
  rotation: SecretRotation
  access: SecretAccess
}

export interface SecretInfo {
  name: string
  type: 'api_key' | 'password' | 'certificate' | 'token' | 'private_key'
  location: string
  encrypted: boolean
  lastRotated: string
  expiry?: string
}

export interface SecretStorage {
  type: 'environment' | 'file' | 'vault' | 'cloud_service'
  encrypted: boolean
  access: string[]
  backup: boolean
}

export interface SecretRotation {
  enabled: boolean
  frequency: string
  automated: boolean
  notification: boolean
}

export interface SecretAccess {
  authentication: boolean
  authorization: string[]
  audit: boolean
  timeLimit: boolean
}

export interface AuthenticationSystem {
  methods: AuthenticationMethod[]
  policies: AuthenticationPolicy[]
  sessions: SessionManagement
  mfa: MFAConfiguration
}

export interface AuthenticationMethod {
  type: 'password' | 'oauth' | 'saml' | 'ldap' | 'certificate' | 'biometric'
  enabled: boolean
  configuration: any
  security: AuthMethodSecurity
}

export interface AuthMethodSecurity {
  encryption: boolean
  hashing: string
  saltRounds?: number
  tokenExpiry?: number
}

export interface AuthenticationPolicy {
  name: string
  rules: PolicyRule[]
  enforcement: 'strict' | 'moderate' | 'lenient'
  exceptions: string[]
}

export interface PolicyRule {
  condition: string
  action: 'allow' | 'deny' | 'challenge'
  priority: number
}

export interface SessionManagement {
  timeout: number
  renewal: boolean
  concurrent: number
  tracking: boolean
  security: SessionSecurity
}

export interface SessionSecurity {
  httpOnly: boolean
  secure: boolean
  sameSite: string
  encryption: boolean
}

export interface MFAConfiguration {
  enabled: boolean
  methods: string[]
  required: boolean
  backup: boolean
}

export interface AuthorizationSystem {
  model: 'rbac' | 'abac' | 'acl' | 'hybrid'
  roles: SecurityRole[]
  permissions: SecurityPermission[]
  policies: AuthorizationPolicy[]
  enforcement: AuthorizationEnforcement
}

export interface SecurityRole {
  name: string
  description: string
  permissions: string[]
  inheritance: string[]
  conditions: string[]
}

export interface SecurityPermission {
  name: string
  resource: string
  actions: string[]
  conditions: string[]
  scope: string
}

export interface AuthorizationPolicy {
  name: string
  rules: AuthorizationRule[]
  effect: 'allow' | 'deny'
  priority: number
}

export interface AuthorizationRule {
  subject: string
  resource: string
  action: string
  condition?: string
}

export interface AuthorizationEnforcement {
  mode: 'strict' | 'permissive'
  caching: boolean
  audit: boolean
  fallback: 'deny' | 'allow'
}

export interface SecurityInfrastructure {
  network: NetworkSecurity
  compute: ComputeSecurity
  storage: StorageSecurity
  monitoring: SecurityMonitoring
}

export interface NetworkSecurity {
  firewalls: FirewallConfig[]
  vpn: VPNConfig
  ssl: SSLConfig
  dns: DNSSecurity
  ddos: DDoSProtection
}

export interface FirewallConfig {
  name: string
  type: 'network' | 'application' | 'web'
  rules: FirewallRule[]
  logging: boolean
  monitoring: boolean
}

export interface FirewallRule {
  name: string
  action: 'allow' | 'deny' | 'log'
  source: string
  destination: string
  port: string
  protocol: string
}

export interface VPNConfig {
  enabled: boolean
  type: 'site_to_site' | 'remote_access'
  encryption: string
  authentication: string
  logging: boolean
}

export interface SSLConfig {
  certificates: SSLCertificate[]
  protocols: string[]
  ciphers: string[]
  hsts: boolean
  ocsp: boolean
}

export interface SSLCertificate {
  domain: string
  issuer: string
  expiry: string
  algorithm: string
  keySize: number
}

export interface DNSSecurity {
  dnssec: boolean
  filtering: boolean
  monitoring: boolean
  logging: boolean
}

export interface DDoSProtection {
  enabled: boolean
  provider: string
  thresholds: DDoSThreshold[]
  mitigation: string[]
}

export interface DDoSThreshold {
  metric: string
  value: number
  action: string
}

export interface ComputeSecurity {
  instances: InstanceSecurity[]
  containers: ContainerSecurity
  serverless: ServerlessSecurity
  patching: PatchManagement
}

export interface InstanceSecurity {
  id: string
  os: string
  hardening: HardeningConfig
  antivirus: AntivirusConfig
  monitoring: boolean
}

export interface HardeningConfig {
  baseline: string
  controls: SecurityControl[]
  compliance: string[]
  audit: boolean
}

export interface SecurityControl {
  id: string
  description: string
  implemented: boolean
  automated: boolean
  evidence: string
}

export interface AntivirusConfig {
  enabled: boolean
  vendor: string
  realTime: boolean
  scheduled: boolean
  quarantine: boolean
}

export interface ContainerSecurity {
  scanning: ContainerScanning
  runtime: RuntimeSecurity
  registry: RegistrySecurity
  policies: ContainerPolicy[]
}

export interface ContainerScanning {
  enabled: boolean
  frequency: string
  vulnerabilities: boolean
  compliance: boolean
  secrets: boolean
}

export interface RuntimeSecurity {
  monitoring: boolean
  policies: string[]
  isolation: boolean
  capabilities: string[]
}

export interface RegistrySecurity {
  authentication: boolean
  authorization: boolean
  scanning: boolean
  signing: boolean
}

export interface ContainerPolicy {
  name: string
  rules: string[]
  enforcement: 'block' | 'warn' | 'log'
}

export interface ServerlessSecurity {
  functions: FunctionSecurity[]
  permissions: ServerlessPermission[]
  monitoring: boolean
  logging: boolean
}

export interface FunctionSecurity {
  name: string
  runtime: string
  permissions: string[]
  environment: boolean
  dependencies: boolean
}

export interface ServerlessPermission {
  function: string
  resource: string
  actions: string[]
  conditions: string[]
}

export interface PatchManagement {
  automated: boolean
  schedule: string
  testing: boolean
  rollback: boolean
  compliance: boolean
}

export interface StorageSecurity {
  encryption: StorageEncryption
  access: StorageAccess
  backup: StorageBackup
  compliance: StorageCompliance
}

export interface StorageEncryption {
  atRest: boolean
  inTransit: boolean
  keyManagement: string
  algorithm: string
}

export interface StorageAccess {
  authentication: boolean
  authorization: string[]
  audit: boolean
  versioning: boolean
}

export interface StorageBackup {
  encrypted: boolean
  frequency: string
  retention: number
  testing: boolean
}

export interface StorageCompliance {
  standards: string[]
  classification: string
  retention: number
  disposal: string
}

export interface SecurityMonitoring {
  siem: SIEMConfig
  ids: IDSConfig
  logging: SecurityLogging
  alerting: SecurityAlerting
}

export interface SIEMConfig {
  enabled: boolean
  vendor: string
  sources: string[]
  rules: SIEMRule[]
  retention: number
}

export interface SIEMRule {
  name: string
  condition: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  action: string
}

export interface IDSConfig {
  enabled: boolean
  type: 'network' | 'host' | 'hybrid'
  signatures: boolean
  anomaly: boolean
  response: string[]
}

export interface SecurityLogging {
  enabled: boolean
  sources: string[]
  format: string
  retention: number
  encryption: boolean
}

export interface SecurityAlerting {
  channels: AlertChannel[]
  rules: AlertRule[]
  escalation: AlertEscalation[]
}

export interface AlertChannel {
  type: 'email' | 'sms' | 'slack' | 'webhook'
  configuration: any
  enabled: boolean
}

export interface AlertRule {
  name: string
  condition: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  channels: string[]
}

export interface AlertEscalation {
  delay: number
  condition: string
  channels: string[]
}

export interface SecurityRequirements {
  standards: SecurityStandard[]
  policies: SecurityPolicy[]
  controls: SecurityControlRequirement[]
  testing: SecurityTesting
  reporting: SecurityReporting
}

export interface SecurityStandard {
  name: string
  version: string
  mandatory: boolean
  controls: string[]
  evidence: string[]
}

export interface SecurityPolicy {
  name: string
  type: 'access' | 'data' | 'incident' | 'compliance'
  rules: string[]
  enforcement: 'mandatory' | 'recommended' | 'optional'
}

export interface SecurityControlRequirement {
  id: string
  category: string
  description: string
  implementation: 'technical' | 'administrative' | 'physical'
  priority: 'high' | 'medium' | 'low'
}

export interface SecurityTesting {
  vulnerability: boolean
  penetration: boolean
  code: boolean
  infrastructure: boolean
  frequency: string
}

export interface SecurityReporting {
  frequency: string
  audience: string[]
  format: string[]
  metrics: string[]
  compliance: boolean
}

export interface SecurityResult {
  taskId: string
  status: 'completed' | 'failed' | 'partial'
  findings: SecurityFinding[]
  vulnerabilities: VulnerabilityReport[]
  compliance: ComplianceReport[]
  recommendations: SecurityRecommendation[]
  metrics: SecurityMetrics
}

export interface SecurityFinding {
  id: string
  type: 'vulnerability' | 'misconfiguration' | 'policy_violation' | 'anomaly'
  severity: 'low' | 'medium' | 'high' | 'critical'
  title: string
  description: string
  location: string
  evidence: string
  impact: string
  remediation: string
  references: string[]
}

export interface VulnerabilityReport {
  summary: VulnerabilitySummary
  details: VulnerabilityDetail[]
  trends: VulnerabilityTrend[]
  recommendations: string[]
}

export interface VulnerabilitySummary {
  total: number
  critical: number
  high: number
  medium: number
  low: number
  fixed: number
  new: number
}

export interface VulnerabilityDetail {
  id: string
  cve?: string
  component: string
  severity: string
  score: number
  description: string
  fix: string
  effort: string
}

export interface VulnerabilityTrend {
  period: string
  discovered: number
  fixed: number
  remaining: number
}

export interface ComplianceReport {
  standard: string
  overall: ComplianceScore
  controls: ComplianceControl[]
  gaps: ComplianceGap[]
  recommendations: string[]
}

export interface ComplianceScore {
  percentage: number
  compliant: number
  nonCompliant: number
  partial: number
  notApplicable: number
}

export interface ComplianceControl {
  id: string
  description: string
  status: 'compliant' | 'non_compliant' | 'partial' | 'not_applicable'
  evidence: string
  gaps: string[]
}

export interface ComplianceGap {
  control: string
  description: string
  impact: string
  remediation: string
  effort: string
}

export interface SecurityRecommendation {
  id: string
  type: 'immediate' | 'short_term' | 'long_term'
  priority: 'critical' | 'high' | 'medium' | 'low'
  category: string
  description: string
  rationale: string
  implementation: string
  effort: string
  impact: string
}

export interface SecurityMetrics {
  riskScore: number
  vulnerabilityDensity: number
  complianceScore: number
  incidentCount: number
  responseTime: number
  coverage: number
}

/**
 * Security Agent - Comprehensive security analysis and compliance
 */
export class SecurityAgent extends BaseAgent {
  private readonly securitySteps = [
    'analyze_codebase', 'scan_vulnerabilities', 'assess_infrastructure',
    'check_compliance', 'analyze_threats', 'test_security',
    'generate_findings', 'create_remediation_plan', 'validate_fixes'
  ]

  constructor(config: Partial<AgentConfig> = {}) {
    super('security', {
      capabilities: {
        requiredCapabilities: [
          'vulnerability_scanning',
          'security_analysis',
          'compliance_checking',
          'threat_modeling',
          'penetration_testing',
          'security_auditing',
          'risk_assessment'
        ],
        contextFilters: ['security', 'vulnerabilities', 'compliance', 'threats', 'code'],
        mcpEnhanced: true,
        sequentialThinking: true,
        contextEnrichment: true,
        ragIntegration: true
      },
      ...config
    })
  }

  /**
   * Execute security workflow
   */
  protected async executeWorkflow(state: AgentState): Promise<AgentState> {
    const input = state.input as SecurityInput
    
    console.log(`🔒 Starting security analysis: ${input.task.title}`)

    // Execute security steps sequentially
    for (const stepId of this.securitySteps) {
      console.log(`🔄 Executing step: ${stepId}`)
      
      const stepResult = await this.executeStepWithContext(stepId, input)
      
      if (stepResult.needsInput) {
        state.needsInput = true
        state.question = stepResult.question
        break
      }

      // Update progress
      state.metadata.completedSteps++
      state.metadata.lastUpdated = new Date().toISOString()
      
      this.emit('step_completed', {
        agentId: this.agentId,
        stepId,
        progress: state.metadata.completedSteps / state.metadata.totalSteps
      })
    }

    // Mark as completed if no input needed
    if (!state.needsInput) {
      state.completed = true
      console.log(`✅ Security analysis completed: ${input.task.title}`)
    }

    return state
  }

  /**
   * Execute individual security step with context enhancement
   */
  private async executeStepWithContext(stepId: string, input?: any): Promise<any> {
    // Get enhanced context from context engine
    const enhancedState = await this.getEnhancedContext(stepId)

    // Execute step with MCP enhancement
    switch (stepId) {
      case 'analyze_codebase':
        return await this.analyzeCodebaseWithMCP(enhancedState, input)
      case 'scan_vulnerabilities':
        return await this.scanVulnerabilitiesWithMCP(enhancedState)
      case 'assess_infrastructure':
        return await this.assessInfrastructureWithMCP(enhancedState)
      case 'check_compliance':
        return await this.checkComplianceWithMCP(enhancedState)
      case 'analyze_threats':
        return await this.analyzeThreatsWithMCP(enhancedState)
      case 'test_security':
        return await this.testSecurityWithMCP(enhancedState)
      case 'generate_findings':
        return await this.generateFindingsWithMCP(enhancedState)
      case 'create_remediation_plan':
        return await this.createRemediationPlanWithMCP(enhancedState)
      case 'validate_fixes':
        return await this.validateFixesWithMCP(enhancedState)
      default:
        throw new Error(`Unknown security step: ${stepId}`)
    }
  }

  /**
   * Get total steps for progress tracking
   */
  protected getTotalSteps(): number {
    return this.securitySteps.length
  }

  /**
   * Get relevant documentation for security
   */
  protected async getRelevantDocumentation(): Promise<Record<string, any>> {
    return {
      security: 'Application security best practices and OWASP guidelines',
      vulnerabilities: 'Vulnerability assessment and management',
      compliance: 'Security compliance frameworks and standards',
      threatModeling: 'Threat modeling methodologies and practices',
      penetrationTesting: 'Penetration testing and security validation',
      secureCode: 'Secure coding practices and code review'
    }
  }

  // ============================================================================
  // STEP IMPLEMENTATIONS WITH MCP ENHANCEMENT
  // ============================================================================

  private async analyzeCodebaseWithMCP(state: any, input: SecurityInput): Promise<any> {
    const analysis = await aiService.analyzeCodebaseSecurity(
      input.codebase,
      input.task.scope
    )

    this.state!.results.codebaseAnalysis = analysis
    
    return {
      results: analysis,
      needsInput: false,
      completed: false
    }
  }

  private async scanVulnerabilitiesWithMCP(state: any): Promise<any> {
    const codebaseAnalysis = this.state!.results.codebaseAnalysis
    
    const vulnerabilities = await aiService.scanVulnerabilities(codebaseAnalysis)

    this.state!.results.vulnerabilities = vulnerabilities
    
    return {
      results: vulnerabilities,
      needsInput: false,
      completed: false
    }
  }

  private async assessInfrastructureWithMCP(state: any): Promise<any> {
    const infrastructure = this.state!.input.infrastructure
    
    const assessment = await aiService.assessInfrastructureSecurity(infrastructure)

    this.state!.results.infrastructureAssessment = assessment
    
    return {
      results: assessment,
      needsInput: false,
      completed: false
    }
  }

  private async checkComplianceWithMCP(state: any): Promise<any> {
    const codebaseAnalysis = this.state!.results.codebaseAnalysis
    const infrastructureAssessment = this.state!.results.infrastructureAssessment
    
    const compliance = await aiService.checkSecurityCompliance(
      codebaseAnalysis,
      infrastructureAssessment,
      this.state!.input.requirements.standards
    )

    this.state!.results.compliance = compliance
    
    return {
      results: compliance,
      needsInput: false,
      completed: false
    }
  }

  private async analyzeThreatsWithMCP(state: any): Promise<any> {
    const allAnalysis = {
      codebase: this.state!.results.codebaseAnalysis,
      infrastructure: this.state!.results.infrastructureAssessment,
      vulnerabilities: this.state!.results.vulnerabilities
    }
    
    const threats = await aiService.analyzeThreatModel(allAnalysis)

    this.state!.results.threats = threats
    
    return {
      results: threats,
      needsInput: false,
      completed: false
    }
  }

  private async testSecurityWithMCP(state: any): Promise<any> {
    const threats = this.state!.results.threats
    
    const testing = await aiService.performSecurityTesting(
      threats,
      this.state!.input.requirements.testing
    )

    this.state!.results.testing = testing
    
    return {
      results: testing,
      needsInput: false,
      completed: false
    }
  }

  private async generateFindingsWithMCP(state: any): Promise<any> {
    const allResults = {
      vulnerabilities: this.state!.results.vulnerabilities,
      compliance: this.state!.results.compliance,
      threats: this.state!.results.threats,
      testing: this.state!.results.testing
    }
    
    const findings = await aiService.generateSecurityFindings(allResults)

    this.state!.results.findings = findings
    
    return {
      results: findings,
      needsInput: false,
      completed: false
    }
  }

  private async createRemediationPlanWithMCP(state: any): Promise<any> {
    const findings = this.state!.results.findings
    
    const remediation = await aiService.createSecurityRemediationPlan(findings)

    this.state!.results.remediation = remediation
    
    return {
      results: remediation,
      needsInput: false,
      completed: false
    }
  }

  private async validateFixesWithMCP(state: any): Promise<any> {
    const remediation = this.state!.results.remediation
    
    const validation = await aiService.validateSecurityFixes(
      remediation,
      this.state!.input.task
    )

    this.state!.results.validation = validation
    
    return {
      results: validation,
      needsInput: false,
      completed: true // Final step
    }
  }
}

// Export for easy access
export { SecurityAgent as default }
