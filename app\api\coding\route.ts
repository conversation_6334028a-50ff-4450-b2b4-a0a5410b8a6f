/**
 * AG3NT Platform - Coding API Routes
 * 
 * API endpoints for autonomous coding workflow
 */

import { NextRequest, NextResponse } from 'next/server'
import { codingOrchestrator } from '@/lib/coding-workflow-orchestrator'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')

    switch (action) {
      case 'status':
        const isRunning = codingOrchestrator.isWorkflowRunning()
        return NextResponse.json({ 
          success: true, 
          data: { isRunning } 
        })

      case 'progress':
        const progress = codingOrchestrator.getProgress()
        return NextResponse.json({ 
          success: true, 
          data: progress 
        })

      case 'tasks':
        const tasks = codingOrchestrator.getTasks()
        return NextResponse.json({ 
          success: true, 
          data: tasks 
        })

      default:
        return NextResponse.json({ 
          success: false, 
          error: 'Invalid action. Use: status, progress, or tasks' 
        }, { status: 400 })
    }
  } catch (error) {
    console.error('Coding API error:', error)
    return NextResponse.json({ 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, ...data } = body

    switch (action) {
      case 'start_coding':
        try {
          if (codingOrchestrator.isWorkflowRunning()) {
            return NextResponse.json({
              success: false,
              error: 'Coding workflow is already running'
            })
          }

          // Validate plan data
          if (!data.plan) {
            return NextResponse.json({
              success: false,
              error: 'Project plan is required'
            })
          }

          console.log('🚀 Starting coding workflow from API...')
          
          // Start coding workflow asynchronously
          codingOrchestrator.startCodingWorkflow(data.plan).catch(error => {
            console.error('Coding workflow error:', error)
          })

          return NextResponse.json({ 
            success: true, 
            message: 'Coding workflow started',
            data: { 
              workflowId: `coding-${Date.now()}`,
              estimatedDuration: '30-45 minutes'
            }
          })

        } catch (error) {
          console.error('Start coding error:', error)
          return NextResponse.json({
            success: false,
            error: error instanceof Error ? error.message : 'Failed to start coding workflow'
          })
        }

      case 'stop_coding':
        try {
          // TODO: Implement stop functionality
          return NextResponse.json({ 
            success: true, 
            message: 'Coding workflow stop requested' 
          })
        } catch (error) {
          console.error('Stop coding error:', error)
          return NextResponse.json({
            success: false,
            error: error instanceof Error ? error.message : 'Failed to stop coding workflow'
          })
        }

      default:
        return NextResponse.json({ 
          success: false, 
          error: 'Invalid action. Use: start_coding or stop_coding' 
        }, { status: 400 })
    }
  } catch (error) {
    console.error('Coding API error:', error)
    return NextResponse.json({ 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    }, { status: 500 })
  }
}
