/**
 * AG3NT Framework - License Manager
 * 
 * Manages license validation, feature gating, and usage tracking
 * for different license tiers (Community, Professional, Enterprise)
 */

import { EventEmitter } from "events"
import crypto from "crypto"

export interface LicenseInfo {
  edition: 'community' | 'professional' | 'enterprise'
  licenseKey?: string
  organizationId?: string
  userId?: string
  features: LicenseFeatures
  limits: LicenseLimits
  expiresAt?: number
  issuedAt: number
  isValid: boolean
}

export interface LicenseFeatures {
  core: boolean
  agents: boolean
  workflows: boolean
  basicMonitoring: boolean
  advancedFeatures: boolean
  collaboration: boolean
  optimization: boolean
  marketplace: boolean
  enterpriseSupport: boolean
  onPremiseDeployment: boolean
  customIntegrations: boolean
  prioritySupport: boolean
}

export interface LicenseLimits {
  maxAgents: number
  maxConcurrentSessions: number
  maxWorkflowComplexity: 'basic' | 'medium' | 'advanced' | 'unlimited'
  maxStorageSize: number // in MB
  maxApiCalls: number // per month
  maxUsers: number
}

export interface UsageMetrics {
  agentsCreated: number
  concurrentSessions: number
  workflowsExecuted: number
  storageUsed: number
  apiCallsThisMonth: number
  activeUsers: number
  lastUpdated: number
}

export interface LicenseValidationResult {
  isValid: boolean
  edition: string
  features: string[]
  limits: LicenseLimits
  usage: UsageMetrics
  warnings: string[]
  errors: string[]
}

/**
 * License Manager - Handles license validation and feature gating
 */
export class LicenseManager extends EventEmitter {
  private licenseInfo: LicenseInfo
  private usageMetrics: UsageMetrics
  private validationCache: Map<string, any> = new Map()
  private lastValidation: number = 0

  constructor(licenseKey?: string) {
    super()
    
    // Initialize with Community Edition by default
    this.licenseInfo = this.getCommunityLicense()
    this.usageMetrics = this.initializeUsageMetrics()

    // Validate license if provided
    if (licenseKey) {
      this.validateLicense(licenseKey)
    }
  }

  /**
   * Validate license key and update license info
   */
  async validateLicense(licenseKey: string): Promise<LicenseValidationResult> {
    try {
      console.log('🔐 Validating AG3NT license...')

      // Check cache first
      const cacheKey = this.generateCacheKey(licenseKey)
      const cached = this.validationCache.get(cacheKey)
      if (cached && Date.now() - this.lastValidation < 3600000) { // 1 hour cache
        return cached
      }

      // Validate license format
      if (!this.isValidLicenseFormat(licenseKey)) {
        throw new Error('Invalid license key format')
      }

      // Decode license information
      const decodedLicense = this.decodeLicense(licenseKey)
      
      // Verify license signature
      if (!this.verifyLicenseSignature(decodedLicense)) {
        throw new Error('Invalid license signature')
      }

      // Check expiration
      if (decodedLicense.expiresAt && Date.now() > decodedLicense.expiresAt) {
        throw new Error('License has expired')
      }

      // Update license info
      this.licenseInfo = {
        ...decodedLicense,
        licenseKey,
        isValid: true
      }

      const result: LicenseValidationResult = {
        isValid: true,
        edition: this.licenseInfo.edition,
        features: Object.keys(this.licenseInfo.features).filter(key => 
          this.licenseInfo.features[key as keyof LicenseFeatures]
        ),
        limits: this.licenseInfo.limits,
        usage: this.usageMetrics,
        warnings: this.generateWarnings(),
        errors: []
      }

      // Cache result
      this.validationCache.set(cacheKey, result)
      this.lastValidation = Date.now()

      this.emit('license_validated', result)
      console.log(`✅ License validated: ${this.licenseInfo.edition} edition`)

      return result

    } catch (error) {
      const result: LicenseValidationResult = {
        isValid: false,
        edition: 'community',
        features: [],
        limits: this.getCommunityLicense().limits,
        usage: this.usageMetrics,
        warnings: [],
        errors: [error instanceof Error ? error.message : 'License validation failed']
      }

      // Fall back to community license
      this.licenseInfo = this.getCommunityLicense()
      
      this.emit('license_validation_failed', result)
      console.warn(`⚠️ License validation failed, using Community Edition: ${result.errors[0]}`)

      return result
    }
  }

  /**
   * Check if a feature is available in current license
   */
  hasFeature(feature: keyof LicenseFeatures): boolean {
    return this.licenseInfo.features[feature] || false
  }

  /**
   * Check if usage is within license limits
   */
  isWithinLimits(): boolean {
    const limits = this.licenseInfo.limits
    const usage = this.usageMetrics

    return (
      usage.agentsCreated <= limits.maxAgents &&
      usage.concurrentSessions <= limits.maxConcurrentSessions &&
      usage.storageUsed <= limits.maxStorageSize &&
      usage.apiCallsThisMonth <= limits.maxApiCalls &&
      usage.activeUsers <= limits.maxUsers
    )
  }

  /**
   * Record usage for tracking
   */
  recordUsage(type: keyof UsageMetrics, value: number): void {
    switch (type) {
      case 'agentsCreated':
        this.usageMetrics.agentsCreated = Math.max(this.usageMetrics.agentsCreated, value)
        break
      case 'concurrentSessions':
        this.usageMetrics.concurrentSessions = Math.max(this.usageMetrics.concurrentSessions, value)
        break
      case 'workflowsExecuted':
        this.usageMetrics.workflowsExecuted += value
        break
      case 'storageUsed':
        this.usageMetrics.storageUsed = value
        break
      case 'apiCallsThisMonth':
        this.usageMetrics.apiCallsThisMonth += value
        break
      case 'activeUsers':
        this.usageMetrics.activeUsers = Math.max(this.usageMetrics.activeUsers, value)
        break
    }

    this.usageMetrics.lastUpdated = Date.now()

    // Check limits and emit warnings
    if (!this.isWithinLimits()) {
      this.emit('usage_limit_exceeded', { type, value, limits: this.licenseInfo.limits })
    }
  }

  /**
   * Get current license information
   */
  getLicenseInfo(): LicenseInfo {
    return { ...this.licenseInfo }
  }

  /**
   * Get current usage metrics
   */
  getUsageMetrics(): UsageMetrics {
    return { ...this.usageMetrics }
  }

  /**
   * Get license status summary
   */
  getLicenseStatus(): any {
    return {
      edition: this.licenseInfo.edition,
      isValid: this.licenseInfo.isValid,
      features: this.licenseInfo.features,
      limits: this.licenseInfo.limits,
      usage: this.usageMetrics,
      utilizationPercentage: this.calculateUtilization(),
      warnings: this.generateWarnings(),
      upgradeRecommendations: this.generateUpgradeRecommendations()
    }
  }

  /**
   * Private helper methods
   */
  private getCommunityLicense(): LicenseInfo {
    return {
      edition: 'community',
      features: {
        core: true,
        agents: true,
        workflows: true,
        basicMonitoring: true,
        advancedFeatures: false,
        collaboration: false,
        optimization: false,
        marketplace: false,
        enterpriseSupport: false,
        onPremiseDeployment: false,
        customIntegrations: false,
        prioritySupport: false
      },
      limits: {
        maxAgents: 10,
        maxConcurrentSessions: 5,
        maxWorkflowComplexity: 'medium',
        maxStorageSize: 100, // 100MB
        maxApiCalls: 1000, // per month
        maxUsers: 1
      },
      issuedAt: Date.now(),
      isValid: true
    }
  }

  private initializeUsageMetrics(): UsageMetrics {
    return {
      agentsCreated: 0,
      concurrentSessions: 0,
      workflowsExecuted: 0,
      storageUsed: 0,
      apiCallsThisMonth: 0,
      activeUsers: 0,
      lastUpdated: Date.now()
    }
  }

  private isValidLicenseFormat(licenseKey: string): boolean {
    // AG3NT license format: AG3NT-XXXX-XXXX-XXXX-XXXX
    const licenseRegex = /^AG3NT-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/
    return licenseRegex.test(licenseKey)
  }

  private decodeLicense(licenseKey: string): LicenseInfo {
    // In a real implementation, this would decode the actual license
    // For demo purposes, we'll simulate different license types based on the key
    
    if (licenseKey.includes('PROF')) {
      return {
        edition: 'professional',
        features: {
          core: true,
          agents: true,
          workflows: true,
          basicMonitoring: true,
          advancedFeatures: true,
          collaboration: true,
          optimization: true,
          marketplace: false,
          enterpriseSupport: false,
          onPremiseDeployment: false,
          customIntegrations: false,
          prioritySupport: true
        },
        limits: {
          maxAgents: 50,
          maxConcurrentSessions: 20,
          maxWorkflowComplexity: 'advanced',
          maxStorageSize: 1000, // 1GB
          maxApiCalls: 10000, // per month
          maxUsers: 10
        },
        issuedAt: Date.now(),
        expiresAt: Date.now() + (365 * 24 * 60 * 60 * 1000), // 1 year
        isValid: true
      }
    }

    if (licenseKey.includes('ENTP')) {
      return {
        edition: 'enterprise',
        features: {
          core: true,
          agents: true,
          workflows: true,
          basicMonitoring: true,
          advancedFeatures: true,
          collaboration: true,
          optimization: true,
          marketplace: true,
          enterpriseSupport: true,
          onPremiseDeployment: true,
          customIntegrations: true,
          prioritySupport: true
        },
        limits: {
          maxAgents: -1, // unlimited
          maxConcurrentSessions: -1, // unlimited
          maxWorkflowComplexity: 'unlimited',
          maxStorageSize: -1, // unlimited
          maxApiCalls: -1, // unlimited
          maxUsers: -1 // unlimited
        },
        issuedAt: Date.now(),
        expiresAt: Date.now() + (365 * 24 * 60 * 60 * 1000), // 1 year
        isValid: true
      }
    }

    // Default to community
    return this.getCommunityLicense()
  }

  private verifyLicenseSignature(license: LicenseInfo): boolean {
    // In a real implementation, this would verify the cryptographic signature
    // For demo purposes, we'll always return true
    return true
  }

  private generateCacheKey(licenseKey: string): string {
    return crypto.createHash('sha256').update(licenseKey).digest('hex')
  }

  private calculateUtilization(): any {
    const limits = this.licenseInfo.limits
    const usage = this.usageMetrics

    return {
      agents: limits.maxAgents > 0 ? (usage.agentsCreated / limits.maxAgents) * 100 : 0,
      sessions: limits.maxConcurrentSessions > 0 ? (usage.concurrentSessions / limits.maxConcurrentSessions) * 100 : 0,
      storage: limits.maxStorageSize > 0 ? (usage.storageUsed / limits.maxStorageSize) * 100 : 0,
      apiCalls: limits.maxApiCalls > 0 ? (usage.apiCallsThisMonth / limits.maxApiCalls) * 100 : 0,
      users: limits.maxUsers > 0 ? (usage.activeUsers / limits.maxUsers) * 100 : 0
    }
  }

  private generateWarnings(): string[] {
    const warnings: string[] = []
    const utilization = this.calculateUtilization()

    if (utilization.agents > 80) {
      warnings.push('Approaching agent limit - consider upgrading license')
    }
    if (utilization.sessions > 80) {
      warnings.push('Approaching concurrent session limit')
    }
    if (utilization.storage > 80) {
      warnings.push('Approaching storage limit')
    }
    if (utilization.apiCalls > 80) {
      warnings.push('Approaching monthly API call limit')
    }

    if (this.licenseInfo.expiresAt && this.licenseInfo.expiresAt - Date.now() < 30 * 24 * 60 * 60 * 1000) {
      warnings.push('License expires within 30 days')
    }

    return warnings
  }

  private generateUpgradeRecommendations(): string[] {
    const recommendations: string[] = []
    const utilization = this.calculateUtilization()

    if (this.licenseInfo.edition === 'community') {
      if (utilization.agents > 50 || utilization.sessions > 50) {
        recommendations.push('Consider upgrading to Professional Edition for higher limits')
      }
      if (!this.licenseInfo.features.advancedFeatures) {
        recommendations.push('Upgrade to Professional Edition to access advanced features')
      }
    }

    if (this.licenseInfo.edition === 'professional') {
      if (utilization.agents > 80 || utilization.sessions > 80) {
        recommendations.push('Consider upgrading to Enterprise Edition for unlimited usage')
      }
      if (!this.licenseInfo.features.enterpriseSupport) {
        recommendations.push('Upgrade to Enterprise Edition for 24/7 support and custom features')
      }
    }

    return recommendations
  }
}

export default LicenseManager
