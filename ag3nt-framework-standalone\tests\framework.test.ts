/**
 * AG3NT Framework - Core Tests
 */

import {
  AG3NT<PERSON>ramework,
  createPlanningAgent,
  createExecutorAgent,
  AgentDiscoveryService,
  LoadBalancer,
  UnifiedContextEngine
} from '../src'

describe('AG3NT Framework', () => {
  let framework: AG3NTFramework

  beforeEach(async () => {
    framework = new AG3NTFramework({
      contextEngine: {
        enableMCP: false,
        enableSequentialThinking: false,
        enableRAG: false
      },
      coordination: {
        enableTaskDelegation: true,
        enableConsensus: true
      },
      discovery: {
        enableAgentDiscovery: true,
        enableLoadBalancing: true
      }
    })
  })

  afterEach(async () => {
    if (framework) {
      await framework.shutdown()
    }
  })

  describe('Framework Initialization', () => {
    test('should initialize successfully', async () => {
      await expect(framework.initialize()).resolves.not.toThrow()
    })

    test('should have correct capabilities', () => {
      const capabilities = framework.getCapabilities()
      expect(capabilities).toBeDefined()
    })
  })

  describe('Agent Management', () => {
    test('should register agents successfully', async () => {
      await framework.initialize()
      
      const planningAgent = createPlanningAgent()
      planningAgent.id = 'test-planning-agent'
      
      await expect(framework.registerAgent(planningAgent)).resolves.not.toThrow()
    })

    test('should discover registered agents', async () => {
      await framework.initialize()
      
      const planningAgent = createPlanningAgent()
      planningAgent.id = 'test-planning-agent'
      await framework.registerAgent(planningAgent)
      
      const discoveryService = framework.getDiscoveryService()
      if (discoveryService) {
        const agents = await discoveryService.discoverAgents({
          agentType: 'planning'
        })
        expect(agents.totalFound).toBeGreaterThan(0)
      }
    })
  })

  describe('Load Balancing', () => {
    test('should route requests to available agents', async () => {
      await framework.initialize()
      
      const executorAgent = createExecutorAgent()
      executorAgent.id = 'test-executor-agent'
      await framework.registerAgent(executorAgent)
      
      const loadBalancer = framework.getLoadBalancer()
      if (loadBalancer) {
        const result = await loadBalancer.routeRequest({
          requestId: 'test-request',
          agentType: 'executor',
          priority: 'medium'
        })
        expect(result.selectedAgent).toBeDefined()
        expect(result.selectedAgent.agentId).toBe('test-executor-agent')
      }
    })
  })

  describe('Context Engine', () => {
    test('should initialize context engine', async () => {
      const contextEngine = new UnifiedContextEngine({
        enableMCP: false,
        enableSequentialThinking: false
      })
      
      await expect(contextEngine.initialize()).resolves.not.toThrow()
      await contextEngine.shutdown()
    })

    test('should register and retrieve agent context', async () => {
      const contextEngine = new UnifiedContextEngine()
      await contextEngine.initialize()
      
      await contextEngine.registerAgent({
        agentType: 'test',
        operationId: 'op-123',
        requiredCapabilities: ['test_capability']
      })
      
      const context = await contextEngine.getContext('test', 'op-123')
      expect(context).toBeDefined()
      
      await contextEngine.shutdown()
    })
  })

  describe('Coordination Systems', () => {
    test('should delegate tasks between agents', async () => {
      await framework.initialize()
      
      const planningAgent = createPlanningAgent()
      planningAgent.id = 'planning-agent'
      const executorAgent = createExecutorAgent()
      executorAgent.id = 'executor-agent'
      
      await framework.registerAgent(planningAgent)
      await framework.registerAgent(executorAgent)
      
      // Test task delegation
      const delegation = await framework.delegateTask(
        'planning-agent',
        'executor-agent',
        {
          type: 'test_task',
          description: 'Test task delegation',
          priority: 'medium'
        }
      )
      
      expect(delegation).toBeDefined()
      expect(delegation.taskId).toBeDefined()
    })
  })

  describe('Analytics', () => {
    test('should provide framework analytics', async () => {
      await framework.initialize()
      
      const analytics = framework.getCoordinationAnalytics()
      expect(analytics).toBeDefined()
      
      const discoveryAnalytics = framework.getDiscoveryAnalytics()
      expect(discoveryAnalytics).toBeDefined()
    })
  })
})

describe('Agent Discovery Service', () => {
  let discoveryService: AgentDiscoveryService

  beforeEach(() => {
    discoveryService = new AgentDiscoveryService({
      enableAutoDiscovery: false,
      discoveryInterval: 1000,
      healthCheckInterval: 500
    })
  })

  afterEach(async () => {
    if (discoveryService) {
      await discoveryService.shutdown()
    }
  })

  test('should start and stop successfully', async () => {
    await expect(discoveryService.start()).resolves.not.toThrow()
    await expect(discoveryService.shutdown()).resolves.not.toThrow()
  })

  test('should register and discover agents', async () => {
    await discoveryService.start()
    
    await discoveryService.registerAgent({
      agentId: 'test-agent',
      agentType: 'test',
      version: '1.0.0',
      capabilities: [],
      endpoint: 'test://localhost'
    })
    
    const result = await discoveryService.discoverAgents({
      agentType: 'test'
    })
    
    expect(result.totalFound).toBe(1)
    expect(result.agents[0].agentId).toBe('test-agent')
  })
})

describe('Load Balancer', () => {
  let discoveryService: AgentDiscoveryService
  let loadBalancer: LoadBalancer

  beforeEach(async () => {
    discoveryService = new AgentDiscoveryService()
    await discoveryService.start()
    
    loadBalancer = new LoadBalancer(discoveryService, {
      algorithm: 'round_robin',
      enableHealthChecks: false,
      enableCircuitBreaker: false
    })
  })

  afterEach(async () => {
    if (loadBalancer) {
      loadBalancer.shutdown()
    }
    if (discoveryService) {
      await discoveryService.shutdown()
    }
  })

  test('should route requests using round robin', async () => {
    // Register test agents
    await discoveryService.registerAgent({
      agentId: 'agent-1',
      agentType: 'test',
      version: '1.0.0',
      capabilities: [],
      endpoint: 'test://agent-1'
    })
    
    await discoveryService.registerAgent({
      agentId: 'agent-2',
      agentType: 'test',
      version: '1.0.0',
      capabilities: [],
      endpoint: 'test://agent-2'
    })
    
    // Route requests
    const result1 = await loadBalancer.routeRequest({
      requestId: 'req-1',
      agentType: 'test',
      priority: 'medium'
    })
    
    const result2 = await loadBalancer.routeRequest({
      requestId: 'req-2',
      agentType: 'test',
      priority: 'medium'
    })
    
    expect(result1.selectedAgent.agentId).toBe('agent-1')
    expect(result2.selectedAgent.agentId).toBe('agent-2')
  })
})

describe('Framework Integration', () => {
  test('should create framework with all features enabled', async () => {
    const framework = new AG3NTFramework({
      contextEngine: {
        enableMCP: true,
        enableSequentialThinking: true,
        enableRAG: true
      },
      coordination: {
        enableTaskDelegation: true,
        enableConsensus: true,
        enableWorkflowHandoffs: true,
        enablePatternRegistry: true
      },
      discovery: {
        enableAgentDiscovery: true,
        enableLoadBalancing: true,
        enableFailover: true,
        loadBalancingAlgorithm: 'adaptive'
      },
      advancedFeatures: {
        adaptiveLearning: { enabled: true },
        temporalDatabase: { enabled: true },
        collaboration: { enabled: true },
        optimization: { enabled: true },
        monitoring: { enabled: true }
      }
    })
    
    await expect(framework.initialize()).resolves.not.toThrow()
    
    // Verify all systems are available
    expect(framework.getDiscoveryService()).toBeDefined()
    expect(framework.getLoadBalancer()).toBeDefined()
    expect(framework.getFailoverManager()).toBeDefined()
    expect(framework.getDelegationSystem()).toBeDefined()
    expect(framework.getConsensusEngine()).toBeDefined()
    
    await framework.shutdown()
  })
})
