"use client"

import type React from "react"

import { useState, useEffect, useRef, useCallback } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { AutoResizeTextarea } from "@/components/ui/auto-resize-textarea"
import { Card, CardContent } from "@/components/ui/card"
import { motion, AnimatePresence } from "framer-motion"
import { ExternalLink, MessageCircle, AlertCircle, Paperclip, X, Loader2, Settings } from "lucide-react"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import type { PlanningTask, Question } from "@/types/planning"
import { ResultsView } from "@/components/results-view"
import Image from "next/image"
import Globe from "@/components/Globe"
import { useFramework } from "@/hooks/use-framework"
import { isolatedFrameworkService } from "@/lib/framework-service-isolated"
import { useCoding } from "@/hooks/use-coding"

const getBasePlanningTasks = (): PlanningTask[] => [
  { id: "analyze", title: "Analyze project requirements", completed: false },
  { id: "clarify", title: "Gather additional details", completed: false },
  { id: "summary", title: "Generate project summary", completed: false },
  { id: "techstack", title: "Select technology stack", completed: false },
  { id: "prd", title: "Create requirements document", completed: false },
]

const getOptionalTasks = (projectType: string): PlanningTask[] => {
  const tasks: PlanningTask[] = []

  // Add context profile for AI agents
  if (isAIAgentProject(projectType)) {
    tasks.push({ id: "context-profile", title: "Generate AI agent context profile", completed: false })
  }

  // Add database design for apps that need databases
  if (needsDatabaseStep(projectType)) {
    tasks.push({ id: "database", title: "Design database schema", completed: false })
  }

  return tasks
}

const getCommonTasks = (): PlanningTask[] => [
  { id: "wireframes", title: "Design UI wireframes", completed: false },
  { id: "design", title: "Create design guidelines", completed: false },
  { id: "filesystem", title: "Plan file structure", completed: false },
  { id: "workflow", title: "Define workflow logic", completed: false },
  { id: "tasks", title: "Break down implementation tasks", completed: false },
  { id: "scaffold", title: "Generate project scaffold", completed: false },
]

const isAIAgentProject = (projectType: string): boolean => {
  if (!projectType) return false
  const lowerType = projectType.toLowerCase()
  return lowerType.includes("agent") ||
         lowerType.includes("bot") ||
         lowerType.includes("ai") ||
         lowerType === "ai agent"
}

const needsDatabaseStep = (projectType: string): boolean => {
  if (!projectType) return false
  const lowerType = projectType.toLowerCase()
  return lowerType.includes("web") ||
         lowerType.includes("app") ||
         lowerType.includes("api") ||
         lowerType.includes("system")
}

const generateDynamicTasks = (projectType: string): PlanningTask[] => {
  return [
    ...getBasePlanningTasks(),
    ...getOptionalTasks(projectType),
    ...getCommonTasks()
  ]
}

export function PlanningAgent() {
  // Framework integration
  const framework = useFramework()
  const coding = useCoding()

  const [userPrompt, setUserPrompt] = useState("")
  const [hasStarted, setHasStarted] = useState(false)
  const [isInteractive, setIsInteractive] = useState(false)
  const [tasks, setTasks] = useState<PlanningTask[]>(getBasePlanningTasks())
  const [currentTaskIndex, setCurrentTaskIndex] = useState(-1)
  const [isProcessing, setIsProcessing] = useState(false)
  const [results, setResults] = useState<Record<string, any>>({})
  const [showResults, setShowResults] = useState(false)
  const [currentQuestion, setCurrentQuestion] = useState<Question | null>(null)
  const [questionAnswer, setQuestionAnswer] = useState("")
  const [userAnswers, setUserAnswers] = useState<Record<string, string>>({})
  const [planningContext, setPlanningContext] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)
  const [canRetry, setCanRetry] = useState(false)
  const [uploadedImages, setUploadedImages] = useState<File[]>([])
  const [isProcessingImages, setIsProcessingImages] = useState(false)
  const [designStyleGuideState, setDesignStyleGuideState] = useState<string | null>(null)

  // Coding workflow state
  const [isCoding, setIsCoding] = useState(false)
  const [codingTasks, setCodingTasks] = useState<any[]>([])
  const [currentCodingTask, setCurrentCodingTask] = useState<any>(null)
  const [activeFile, setActiveFile] = useState<string | null>(null)
  const [liveCode, setLiveCode] = useState<string>("")

  // Settings state
  const [isSettingsOpen, setIsSettingsOpen] = useState(false)
  const [userApiKey, setUserApiKey] = useState("")
  const [preferredModel, setPreferredModel] = useState("anthropic/claude-sonnet-4")
  const [isAutonomousMode, setIsAutonomousMode] = useState(true)

  // UI state
  const [activeTab, setActiveTab] = useState<"preview" | "code" | "planning" | "graph">("preview")
  const [chatMessages, setChatMessages] = useState<Array<{id: string, type: 'user' | 'ai', content: string, timestamp: Date}>>([])
  const [chatInput, setChatInput] = useState("")
  const [sidebarWidth, setSidebarWidth] = useState(500) // Default width for SSR (1/3 page width)
  const [isResizing, setIsResizing] = useState(false)
  const [selectedPlanningSection, setSelectedPlanningSection] = useState<string | null>(null)

  const taskListRef = useRef<HTMLDivElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const resizeRef = useRef<HTMLDivElement>(null)
  // Removed isUsingFallback - using AI service only

  // Set proper sidebar width after hydration
  useEffect(() => {
    if (typeof window !== "undefined") {
      const calculatedWidth = Math.floor((window.innerWidth - 48) / 3)
      setSidebarWidth(calculatedWidth)
    }
  }, [])

  // Resize functionality
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    setIsResizing(true)
    e.preventDefault()
  }, [])

  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!isResizing) return

      const newWidth = e.clientX - 16 // Account for padding
      const minWidth = 300
      const maxWidth = window.innerWidth * 0.6

      if (newWidth >= minWidth && newWidth <= maxWidth) {
        setSidebarWidth(newWidth)
      }
    }

    const handleMouseUp = () => {
      setIsResizing(false)
    }

    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
      document.body.style.cursor = 'col-resize'
      document.body.style.userSelect = 'none'
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
      document.body.style.cursor = ''
      document.body.style.userSelect = ''
    }
  }, [isResizing])

  // Auto-scroll to current task
  useEffect(() => {
    if (taskListRef.current && currentTaskIndex >= 0) {
      const taskElements = taskListRef.current.querySelectorAll('[data-task-index]')
      const currentTaskElement = taskElements[currentTaskIndex] as HTMLElement

      if (currentTaskElement) {
        const container = taskListRef.current
        const containerHeight = container.clientHeight
        const taskTop = currentTaskElement.offsetTop
        const taskHeight = currentTaskElement.offsetHeight

        // Calculate scroll position to center the current task
        const scrollTop = taskTop - (containerHeight / 2) + (taskHeight / 2)

        container.scrollTo({
          top: Math.max(0, scrollTop),
          behavior: 'smooth'
        })
      }
    }
  }, [currentTaskIndex, tasks.length])

  const processNextTask = async (context: any) => {
    console.log(`processNextTask called with currentTaskIndex: ${currentTaskIndex}`)

    if (currentTaskIndex < 0 || currentTaskIndex >= tasks.length) {
      console.log("Invalid task index, finishing processing")
      setIsProcessing(false)
      return
    }

    const currentTask = tasks[currentTaskIndex]
    if (!currentTask) {
      console.log("No current task found, finishing processing")
      setIsProcessing(false)
      return
    }

    console.log(`Processing task: ${currentTask.id} (index: ${currentTaskIndex})`)
    console.log(`Context keys:`, Object.keys(context || {}))
    console.log(`Has design style guide:`, !!context.designStyleGuide)
    if (context?.designStyleGuide) {
      console.log("Design style guide length:", context.designStyleGuide.length)
    }

    // Ensure design style guide is preserved from multiple sources
    const designStyleGuide = context.designStyleGuide || planningContext?.designStyleGuide || designStyleGuideState
    console.log("Design style guide sources:")
    console.log("- context.designStyleGuide:", !!context.designStyleGuide)
    console.log("- planningContext?.designStyleGuide:", !!planningContext?.designStyleGuide)
    console.log("- designStyleGuideState:", !!designStyleGuideState)
    console.log("- final designStyleGuide:", !!designStyleGuide)
    if (designStyleGuide && !context.designStyleGuide) {
      console.log("Restoring design style guide from state/planningContext")
    }

    try {
      const response = await fetch("/api/planning/step", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          step: currentTask.id,
          context: {
            ...context,
            prompt: userPrompt,
            isInteractive: isInteractive,
            userAnswers: userAnswers,
            designStyleGuide: designStyleGuide, // Always include design style guide if available
            hasImages: uploadedImages.length > 0,
            // Include user preferences for each step
            userPreferences: {
              model: preferredModel,
              apiKey: userApiKey || undefined,
              autonomousMode: isAutonomousMode
            }
          },
          answer: questionAnswer,
        }),
      })

      // Check if response is ok first
      if (!response.ok) {
        const errorText = await response.text()
        console.error(`API Error ${response.status}:`, errorText)
        throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`)
      }

      // Check content type
      const contentType = response.headers.get("content-type")
      if (!contentType || !contentType.includes("application/json")) {
        const textResponse = await response.text()
        console.error("Non-JSON response received:", textResponse.substring(0, 200))
        throw new Error("Server returned non-JSON response")
      }

      const responseText = await response.text()
      console.log(`API response for ${currentTask.id}:`, responseText.substring(0, 200) + "...")

      let result
      try {
        result = JSON.parse(responseText)
        console.log(`Parsed result for ${currentTask.id}:`, result)
      } catch (parseError) {
        console.error("Failed to parse response as JSON:", parseError)
        console.error("Raw response:", responseText.substring(0, 500))
        throw new Error("Invalid JSON response from API")
      }

      // Validate result structure
      if (!result || typeof result !== "object") {
        throw new Error("Invalid result structure")
      }

      // Check if we need user input
      if (result.needsInput && result.question && isInteractive) {
        console.log("Need user input:", result.question.question)
        setCurrentQuestion(result.question)
        setPlanningContext({ ...context, ...result })
        return
      }

      // Check if there's an error but we have fallback results
      if (result.error && result.results) {
        console.log("Using fallback results due to error:", result.error)
        setError(`Step processing issue: ${result.error}`)
      }

      // Mark task as completed and store results
      setTasks((prev) => {
        const updated = prev.map((task, index) => (index === currentTaskIndex ? { ...task, completed: true } : task))
        console.log(`Marked task ${currentTaskIndex} (${currentTask.id}) as completed`)
        return updated
      })

      // If this is the analyze step, update the task list based on project type
      if (currentTask.id === "analyze" && result.results?.analyze?.projectType) {
        const projectType = result.results.analyze.projectType
        console.log(`Project type detected: ${projectType}, updating task list...`)
        const newTasks = generateDynamicTasks(projectType)

        // Preserve completion status for already completed tasks
        const updatedTasks = newTasks.map((newTask, index) => {
          if (index < currentTaskIndex) {
            // Tasks before current should be completed
            return { ...newTask, completed: true }
          } else if (index === currentTaskIndex) {
            // Current task should be marked as completed since we just finished it
            return { ...newTask, completed: true }
          }
          return newTask
        })

        setTasks(updatedTasks)
        console.log(`Updated task list with ${updatedTasks.length} tasks for project type: ${projectType}`)
      }

      // Store the result data
      if (result.results && result.results[currentTask.id]) {
        // For all tasks, store the result returned from the planning step API.
        // The API already handles design step correctly by returning Gemini design when available
        setResults((prev) => ({ ...prev, [currentTask.id]: result.results[currentTask.id] }));
        console.log(`Stored result for ${currentTask.id}:`, result.results[currentTask.id]);
      } else {
        // Warn if no result data is available for any step.
        console.warn(`No result data for ${currentTask.id}`);
      }

      // Update planning context while preserving design style guide
      const preservedDesignStyleGuide = designStyleGuide || context.designStyleGuide || planningContext?.designStyleGuide || designStyleGuideState
      setPlanningContext({
        ...context,
        ...result,
        designStyleGuide: preservedDesignStyleGuide
      })

      // Move to next task
      if (currentTaskIndex < tasks.length - 1) {
        console.log(`Moving to next task (${currentTaskIndex + 1})`)
        setTimeout(() => {
          setCurrentTaskIndex((prev) => prev + 1)
        }, 1000)
      } else {
        console.log("All tasks completed!")
        // Add delay to allow UI to update and show final task as completed
        setTimeout(async () => {
          setIsProcessing(false)
          // Auto-start coding workflow
          await startCodingWorkflow()
        }, 1000)
      }
    } catch (error) {
      console.error("Failed to process step:", error)

      // Enhanced error handling for step processing
      let errorMessage = `AI step processing failed for ${currentTask.title}. `
      let shouldRetry = false

      if (error instanceof Error) {
        if (error.message.includes("429")) {
          errorMessage += "Rate limit exceeded."
          shouldRetry = true
        } else if (error.message.includes("timeout")) {
          errorMessage += "Request timed out."
          shouldRetry = true
        } else if (error.message.includes("network")) {
          errorMessage += "Network error occurred."
          shouldRetry = true
        } else {
          errorMessage += error.message
        }
      } else {
        errorMessage += "Unknown error occurred."
      }

      setError(errorMessage + (shouldRetry ? " You can try again." : " Please check your AI service configuration."))
      setCanRetry(shouldRetry)

      // Stop processing on error - no fallback to mock data
      setIsProcessing(false)
    }
  }

  const handleStartPlanning = async () => {
    if (!userPrompt.trim()) return

    console.log("Starting planning process...")
    setHasStarted(true)
    setIsProcessing(true)
    setCurrentTaskIndex(0)
    setError(null)

    // Add user message to chat
    const userMessage = {
      id: Date.now().toString(),
      type: 'user' as const,
      content: userPrompt,
      timestamp: new Date()
    }
    setChatMessages(prev => [...prev, userMessage])

    // Add AI response
    setTimeout(() => {
      const aiMessage = {
        id: (Date.now() + 1).toString(),
        type: 'ai' as const,
        content: "I'll help you build that! Let me start by analyzing your requirements and creating a development plan.",
        timestamp: new Date()
      }
      setChatMessages(prev => [...prev, aiMessage])
    }, 500)

    try {
      let designStyleGuide = null

      // If images are uploaded, use the design agent first
      if (uploadedImages.length > 0) {
        console.log("Processing uploaded images with design agent...")
        setIsProcessingImages(true)

        try {
          const formData = new FormData()
          uploadedImages.forEach((image, index) => {
            formData.append('images', image)
          })

          const designResponse = await fetch("/api/design-agent", {
            method: "POST",
            body: formData,
          })

          if (designResponse.ok) {
            const designResult = await designResponse.json()
            designStyleGuide = designResult.styleGuide
            setDesignStyleGuideState(designStyleGuide) // Store in state for persistence
            console.log("Design agent generated style guide - will be added when design step runs")
          } else {
            const errorText = await designResponse.text()
            console.warn("Design agent failed:", errorText)
            // Continue without style guide but show a warning
          }
        } catch (designError) {
          console.error("Design agent error:", designError)
          // Continue without style guide
        } finally {
          setIsProcessingImages(false)
        }
      }

      // Try to use Isolated AG3NT Framework for planning
      console.log("Using Isolated AG3NT Framework for planning...")

      try {
        // Use isolated framework service directly
        await isolatedFrameworkService.initialize()

        const planResult = await isolatedFrameworkService.planProject({
          projectName: extractProjectName(userPrompt),
          projectDescription: userPrompt,
          projectType: 'web-application',
          frontendFramework: 'react',
          backendFramework: 'nestjs',
          database: 'postgresql',
          features: extractFeatures(userPrompt)
        })

        if (planResult.success) {
          console.log("Isolated AG3NT Framework planning completed:", planResult)
          console.log("Setting planning context with framework result")
          setPlanningContext({
            ...planResult.data,
            designStyleGuide: designStyleGuide,
            frameworkResult: planResult,
            agentsUsed: planResult.agentsUsed,
            executionTime: planResult.executionTime
          })
        } else {
          throw new Error(planResult.error || "Framework planning failed")
        }
      } catch (frameworkError) {
        console.warn("Isolated framework planning failed, falling back to API:", frameworkError.message)

        // Fallback to original API planning
        const response = await fetch("/api/planning", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            prompt: userPrompt,
            isInteractive,
            answers: userAnswers,
            designStyleGuide,
            hasImages: uploadedImages.length > 0,
            userPreferences: {
              model: preferredModel,
              apiKey: userApiKey || undefined,
              autonomousMode: isAutonomousMode
            }
          }),
        })

        const responseText = await response.text()
        let result
        try {
          result = JSON.parse(responseText)
        } catch (parseError) {
          throw new Error("Invalid JSON response from planning API")
        }

        if (!response.ok) {
          throw new Error(`API Error: ${response.status} - ${result.error || "Unknown error"}`)
        }

        console.log("Using fallback API planning")
        setPlanningContext({
          ...result,
          designStyleGuide: designStyleGuide,
        })
      }
      // Start AI-based processing
      console.log("Setting currentTaskIndex to 0 and starting processing")
      setCurrentTaskIndex(0)
    } catch (error) {
      console.error("Failed to start AI planning:", error)

      // Enhanced error handling with specific error types
      let errorMessage = "AI planning failed. "
      let suggestion = ""

      if (error instanceof Error) {
        if (error.message.includes("401") || error.message.includes("403")) {
          errorMessage += "Authentication failed."
          suggestion = "Please check your OPENROUTER_API_KEY is valid and has sufficient credits."
        } else if (error.message.includes("429")) {
          errorMessage += "Rate limit exceeded."
          suggestion = "Please wait a moment and try again."
        } else if (error.message.includes("network") || error.message.includes("fetch")) {
          errorMessage += "Network connection failed."
          suggestion = "Please check your internet connection and try again."
        } else {
          errorMessage += error.message
          suggestion = "Please check your OPENROUTER_API_KEY and try again."
        }
      } else {
        errorMessage += "Unknown error occurred."
        suggestion = "Please refresh the page and try again."
      }

      setError(`${errorMessage} ${suggestion}`)
      // Do not fallback to mock data - show error instead
      setIsProcessing(false)
    }
  }

  // Auto-start coding workflow when planning completes
  const startCodingWorkflow = async () => {
    try {
      console.log("🚀 Planning completed! Starting autonomous coding workflow...")

      // Set coding state
      setIsCoding(true)

      // Initialize coding tasks for UI display
      const initialCodingTasks = [
        { id: 'db-schema', title: 'Create Database Schema', status: 'pending', type: 'database' },
        { id: 'backend-setup', title: 'Setup Backend Project', status: 'pending', type: 'backend' },
        { id: 'backend-api', title: 'Generate API Endpoints', status: 'pending', type: 'backend' },
        { id: 'frontend-setup', title: 'Setup Frontend Project', status: 'pending', type: 'frontend' },
        { id: 'frontend-components', title: 'Generate UI Components', status: 'pending', type: 'frontend' },
        { id: 'frontend-styling', title: 'Apply Design System', status: 'pending', type: 'frontend' },
        { id: 'api-integration', title: 'Integrate Frontend with API', status: 'pending', type: 'integration' },
        { id: 'unit-tests', title: 'Generate Unit Tests', status: 'pending', type: 'testing' },
        { id: 'integration-tests', title: 'Generate Integration Tests', status: 'pending', type: 'testing' },
        { id: 'deployment-config', title: 'Setup Deployment Configuration', status: 'pending', type: 'deployment' }
      ]

      setCodingTasks(initialCodingTasks)

      // Prepare project plan from planning context
      const projectPlan = {
        projectName: extractProjectName(userPrompt),
        projectDescription: userPrompt,
        architecture: planningContext?.architecture || {},
        techStack: planningContext?.techStack || {},
        features: extractFeatures(userPrompt),
        wireframes: planningContext?.wireframes || {},
        design: planningContext?.design || {},
        filesystem: planningContext?.filesystem || {},
        workflow: planningContext?.workflow || {},
        tasks: planningContext?.tasks || {}
      }

      // Start coding workflow
      const result = await coding.startCoding(projectPlan)

      if (result.success) {
        console.log("✅ Coding workflow started successfully!")
        // Start monitoring coding progress
        startCodingProgressMonitoring()
      } else {
        console.error("❌ Failed to start coding workflow:", result.error)
        setIsCoding(false)
      }

    } catch (error) {
      console.error("❌ Error starting coding workflow:", error)
      setIsCoding(false)
    }
  }

  // Coding progress monitoring
  const startCodingProgressMonitoring = () => {
    const interval = setInterval(async () => {
      try {
        // Fetch coding progress
        const progressResponse = await fetch('/api/coding?action=progress')
        const progressResult = await progressResponse.json()

        if (progressResult.success) {
          const progress = progressResult.data

          // Update coding tasks with real progress
          setCodingTasks(prevTasks =>
            prevTasks.map(task => {
              const realTask = coding.tasks.find(t => t.id === task.id)
              if (realTask) {
                return { ...task, status: realTask.status, output: realTask.output }
              }
              return task
            })
          )

          // Check if coding is complete
          if (!coding.isRunning && progress.completedTasks === progress.totalTasks) {
            setIsCoding(false)
            clearInterval(interval)
            console.log("🎉 Autonomous coding completed!")
          }
        }

        // Simulate live file generation for demo
        simulateLiveFileGeneration()

      } catch (error) {
        console.error("Error monitoring coding progress:", error)
      }
    }, 2000) // Update every 2 seconds
  }

  // Simulate live file generation for the code tab
  const simulateLiveFileGeneration = () => {
    const currentTask = codingTasks.find(task => task.status === 'in_progress')
    if (!currentTask) return

    // Simulate different files being worked on
    const filesByTask = {
      'db-schema': ['prisma/schema.prisma', 'prisma/migrations/001_init.sql'],
      'backend-setup': ['src/main.ts', 'src/app.module.ts', 'package.json'],
      'backend-api': ['src/calculator/calculator.controller.ts', 'src/calculator/calculator.service.ts'],
      'frontend-setup': ['src/App.tsx', 'src/main.tsx', 'vite.config.ts'],
      'frontend-components': ['src/components/Calculator.tsx', 'src/components/Display.tsx', 'src/components/Button.tsx'],
      'frontend-styling': ['src/styles/cyberpunk.css', 'src/styles/neon-effects.css'],
      'api-integration': ['src/services/api.ts', 'src/services/calculator.ts'],
      'unit-tests': ['src/components/__tests__/Calculator.test.tsx'],
      'integration-tests': ['tests/e2e/calculator.spec.ts'],
      'deployment-config': ['vercel.json', 'Dockerfile']
    }

    const files = filesByTask[currentTask.id] || []
    if (files.length > 0) {
      const randomFile = files[Math.floor(Math.random() * files.length)]
      setActiveFile(randomFile)

      // Simulate code generation
      generateLiveCode(randomFile)
    }
  }

  // Generate live code content for the active file
  const generateLiveCode = (fileName: string) => {
    const codeTemplates = {
      'Calculator.tsx': `import React, { useState } from 'react'
import { Button } from './Button'
import { Display } from './Display'

export function Calculator() {
  const [display, setDisplay] = useState('0')
  const [operation, setOperation] = useState<string | null>(null)
  const [previousValue, setPreviousValue] = useState<number | null>(null)

  const handleNumber = (num: string) => {
    if (display === '0') {
      setDisplay(num)
    } else {
      setDisplay(display + num)
    }
  }

  const handleOperation = (op: string) => {
    const current = parseFloat(display)
    if (previousValue === null) {
      setPreviousValue(current)
    } else if (operation) {
      const result = calculate(previousValue, current, operation)
      setDisplay(String(result))
      setPreviousValue(result)
    }
    setOperation(op)
    setDisplay('0')
  }

  const calculate = (prev: number, current: number, op: string): number => {
    switch (op) {
      case '+': return prev + current
      case '-': return prev - current
      case '*': return prev * current
      case '/': return prev / current
      default: return current
    }
  }

  return (
    <div className="calculator cyberpunk-theme">
      <Display value={display} />
      <div className="button-grid">
        {/* Number buttons */}
        {[7, 8, 9, 4, 5, 6, 1, 2, 3, 0].map(num => (
          <Button
            key={num}
            onClick={() => handleNumber(String(num))}
            className="number-btn neon-glow"
          >
            {num}
          </Button>
        ))}

        {/* Operation buttons */}
        {['+', '-', '*', '/'].map(op => (
          <Button
            key={op}
            onClick={() => handleOperation(op)}
            className="operation-btn neon-pulse"
          >
            {op}
          </Button>
        ))}
      </div>
    </div>
  )
}`,
      'cyberpunk.css': `.cyberpunk-theme {
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 100%);
  color: #00ffff;
  font-family: 'Orbitron', monospace;
}

.neon-glow {
  box-shadow:
    0 0 5px #00ffff,
    0 0 10px #00ffff,
    0 0 15px #00ffff,
    0 0 20px #00ffff;
  border: 1px solid #00ffff;
  background: rgba(0, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.neon-glow:hover {
  box-shadow:
    0 0 10px #ff00ff,
    0 0 20px #ff00ff,
    0 0 30px #ff00ff,
    0 0 40px #ff00ff;
  border-color: #ff00ff;
  color: #ff00ff;
}

.neon-pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { box-shadow: 0 0 5px #00ffff; }
  50% { box-shadow: 0 0 20px #00ffff, 0 0 30px #ff00ff; }
  100% { box-shadow: 0 0 5px #00ffff; }
}

.calculator {
  max-width: 400px;
  margin: 0 auto;
  padding: 20px;
  border-radius: 15px;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
}

.button-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 10px;
  margin-top: 20px;
}`,
      'schema.prisma': `generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id          String   @id @default(cuid())
  email       String   @unique
  name        String?
  calculations Calculation[]
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("users")
}

model Calculation {
  id         String   @id @default(cuid())
  expression String
  result     Float
  userId     String
  user       User     @relation(fields: [userId], references: [id])
  createdAt  DateTime @default(now())

  @@map("calculations")
}

model CalculationHistory {
  id            String   @id @default(cuid())
  userId        String
  calculations  Json[]
  sessionId     String?
  createdAt     DateTime @default(now())

  @@map("calculation_history")
}`
    }

    // Get template or generate basic content
    let content = codeTemplates[fileName.split('/').pop() || ''] || `// Generated file: ${fileName}
// This file is being created by the AG3NT Framework

export default {
  // Implementation will be generated here
}`

    // Simulate typing effect by showing partial content
    const lines = content.split('\n')
    const partialContent = lines.slice(0, Math.floor(Math.random() * lines.length) + 1).join('\n')
    setLiveCode(partialContent)
  }

  // Helper functions for framework integration
  const extractProjectName = (prompt: string): string => {
    // Ensure prompt is a string
    if (!prompt || typeof prompt !== 'string') {
      return `Project from ${new Date().toLocaleDateString()}`
    }

    // Simple extraction - could be enhanced with NLP
    const words = prompt.toLowerCase().split(' ')
    const appIndex = words.findIndex(word =>
      word.includes('app') ||
      word.includes('application') ||
      word.includes('website') ||
      word.includes('platform') ||
      word.includes('calculator') ||
      word.includes('tool')
    )

    if (appIndex > 0) {
      return words.slice(Math.max(0, appIndex - 2), appIndex + 1).join(' ')
    }

    // Try to extract the main concept
    const concepts = ['calculator', 'dashboard', 'chat', 'blog', 'shop', 'game']
    for (const concept of concepts) {
      if (prompt.toLowerCase().includes(concept)) {
        return `${concept} app`
      }
    }

    return `Project from ${new Date().toLocaleDateString()}`
  }

  const extractFeatures = (prompt: string): string[] => {
    const features: string[] = []

    // Ensure prompt is a string
    if (!prompt || typeof prompt !== 'string') {
      return ['basic_crud', 'responsive_design']
    }

    const lowerPrompt = prompt.toLowerCase()

    // Common features to detect
    const featureMap = {
      'auth': ['login', 'signin', 'signup', 'authentication', 'user'],
      'realtime': ['realtime', 'real-time', 'live', 'websocket'],
      'database': ['database', 'data', 'store', 'save'],
      'api': ['api', 'backend', 'server'],
      'responsive': ['mobile', 'responsive', 'device'],
      'dashboard': ['dashboard', 'admin', 'analytics'],
      'payment': ['payment', 'stripe', 'billing', 'subscription'],
      'search': ['search', 'filter', 'find'],
      'notifications': ['notification', 'alert', 'email'],
      'chat': ['chat', 'message', 'communication'],
      'calculator': ['calculator', 'math', 'calculation', 'compute'],
      'ui_effects': ['neon', 'glow', 'cyberpunk', 'animation', 'effects']
    }

    for (const [feature, keywords] of Object.entries(featureMap)) {
      if (keywords.some(keyword => lowerPrompt.includes(keyword))) {
        features.push(feature)
      }
    }

    return features.length > 0 ? features : ['basic_crud', 'responsive_design']
  }

  const retryCurrentStep = () => {
    setError(null)
    setCanRetry(false)
    setIsProcessing(true)

    // Retry the current step
    if (currentTaskIndex >= 0 && currentTaskIndex < tasks.length) {
      setTimeout(() => processNextTask(planningContext || {}), 500)
    } else {
      // Retry from the beginning
      setTimeout(() => handleStartPlanning(), 500)
    }
  }

  const handleQuestionSubmit = async () => {
    if (currentQuestion) {
      setUserAnswers((prev) => ({ ...prev, [currentQuestion.id]: questionAnswer }))
      setQuestionAnswer("")
      setCurrentQuestion(null)

      // Continue processing with the answer using AI
      setTimeout(() => processNextTask(planningContext), 500)
    }
  }

  const handleQuestionKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleQuestionSubmit()
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleStartPlanning()
    }
  }

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files) {
      const imageFiles = Array.from(files).filter(file => {
        // Validate file type
        if (!file.type.startsWith('image/')) {
          return false
        }
        // Validate file size (max 10MB)
        if (file.size > 10 * 1024 * 1024) {
          alert(`File ${file.name} is too large. Maximum size is 10MB.`)
          return false
        }
        return true
      })

      // Limit total number of images to 5
      const currentCount = uploadedImages.length
      const newFiles = imageFiles.slice(0, Math.max(0, 5 - currentCount))

      if (newFiles.length < imageFiles.length) {
        alert(`Maximum 5 images allowed. Only the first ${newFiles.length} images were added.`)
      }

      setUploadedImages(prev => [...prev, ...newFiles])
    }
    // Reset the input value to allow uploading the same file again
    e.target.value = ''
  }

  const removeImage = (index: number) => {
    setUploadedImages(prev => prev.filter((_, i) => i !== index))
  }

  const triggerImageUpload = () => {
    fileInputRef.current?.click()
  }

  const clearAllImages = () => {
    setUploadedImages([])
  }

  useEffect(() => {
    if (currentTaskIndex >= 0 && currentTaskIndex < tasks.length && isProcessing && !currentQuestion) {
      console.log(`useEffect triggered for task index: ${currentTaskIndex}, task: ${tasks[currentTaskIndex]?.id}`)
      processNextTask(planningContext || {})
    }
  }, [currentTaskIndex, isProcessing, currentQuestion])

  // Auto-select first section when planning completes
  useEffect(() => {
    if (!isProcessing && Object.keys(results).length > 0 && !selectedPlanningSection) {
      const firstSection = Object.keys(results)[0]
      if (firstSection) {
        setSelectedPlanningSection(firstSection)
      }
    }
  }, [isProcessing, results, selectedPlanningSection])

  const openResults = () => {
    setShowResults(true)
  }

  const backToPlanning = () => {
    setShowResults(false)
  }

  // Export functionality
  const handleExport = () => {
    const exportData = {
      prompt: userPrompt,
      timestamp: new Date().toISOString(),
      results: results,
    }

    const dataStr = JSON.stringify(exportData, null, 2)
    const dataBlob = new Blob([dataStr], { type: "application/json" })
    const url = URL.createObjectURL(dataBlob)

    const link = document.createElement("a")
    link.href = url
    link.download = `project-plan-${new Date().toISOString().split("T")[0]}.json`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }

  if (showResults) {
    return <ResultsView results={results} userPrompt={userPrompt} onBack={backToPlanning} />
  }

  // Question overlay (only shown in interactive mode)
  if (currentQuestion && isInteractive) {
    return (
      <div className="min-h-screen bg-black text-white flex items-center justify-center p-4">
        <div className="w-full max-w-md">
          <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} className="text-center space-y-6">
            <div className="flex items-center justify-center gap-2 mb-4">
              <MessageCircle className="w-5 h-5 text-red-400" />
              <span className="text-sm text-gray-400">
                <span className="text-white">AG3N</span>
                <span className="text-[#ff2d55]">T</span>
                <span className="text-gray-400"> is asking</span>
              </span>
            </div>

            <h2 className="text-2xl font-light text-white">{currentQuestion.question}</h2>

            <div className="space-y-4">
              {currentQuestion.type === "text" ? (
                <Input
                  value={questionAnswer}
                  onChange={(e) => setQuestionAnswer(e.target.value)}
                  onKeyPress={handleQuestionKeyPress}
                  placeholder={currentQuestion.placeholder}
                  className="w-full h-12 text-lg bg-white text-black border-0 rounded-none placeholder-gray-500 focus:ring-2 focus:ring-red-400"
                  autoFocus
                />
              ) : (
                <Textarea
                  value={questionAnswer}
                  onChange={(e) => setQuestionAnswer(e.target.value)}
                  onKeyPress={handleQuestionKeyPress}
                  placeholder={currentQuestion.placeholder}
                  className="w-full min-h-[100px] text-lg bg-white text-black border-0 rounded-none placeholder-gray-500 focus:ring-2 focus:ring-red-400 resize-none"
                  autoFocus
                />
              )}

              <div className="flex gap-3">
                <Button
                  onClick={handleQuestionSubmit}
                  disabled={!questionAnswer.trim() && !currentQuestion.optional}
                  className="flex-1 h-12 bg-red-600 hover:bg-red-700 text-white rounded-none font-medium"
                >
                  Continue
                </Button>
                {currentQuestion.optional && (
                  <Button
                    onClick={() => {
                      setCurrentQuestion(null)
                      setTimeout(() => processNextTask(planningContext), 500)
                    }}
                    variant="outline"
                    className="h-12 px-6 border-gray-600 text-gray-300 hover:bg-gray-800 rounded-none"
                  >
                    Skip
                  </Button>
                )}
              </div>
            </div>

            {currentQuestion.optional && <p className="text-xs text-gray-500">This question is optional</p>}
          </motion.div>
        </div>
      </div>
    )
  }

  if (!hasStarted) {
    return (
      <div className="min-h-screen bg-black text-white flex items-center justify-center p-4 relative">
        {/* Settings Button - Bottom Left */}
        <Dialog open={isSettingsOpen} onOpenChange={setIsSettingsOpen}>
          <DialogTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              className="fixed bottom-6 left-6 h-8 w-8 text-gray-400 hover:text-white transition-colors duration-200 z-50 bg-transparent border-0 p-0"
            >
              <Settings size={20} />
              <span className="sr-only">Settings</span>
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-md border-gray-700 text-white" style={{ backgroundColor: '#181818' }}>
            <DialogHeader>
              <DialogTitle className="text-white">Settings</DialogTitle>
            </DialogHeader>
            <div className="space-y-6 py-4">
              {/* API Key Input */}
              <div className="space-y-2">
                <Label htmlFor="api-key" className="text-sm font-medium text-gray-300">
                  OpenRouter API Key
                </Label>
                <Input
                  id="api-key"
                  type="password"
                  placeholder="sk-or-v1-..."
                  value={userApiKey}
                  onChange={(e) => setUserApiKey(e.target.value)}
                  className="bg-gray-800 border-gray-600 text-white placeholder-gray-400 focus:border-blue-500"
                />
                <p className="text-xs text-gray-400">
                  Optional: Use your own API key for unlimited usage
                </p>
              </div>

              {/* Model Selection */}
              <div className="space-y-2">
                <Label htmlFor="model" className="text-sm font-medium text-gray-300">
                  Preferred Model
                </Label>
                <Select value={preferredModel} onValueChange={setPreferredModel}>
                  <SelectTrigger className="bg-gray-800 border-gray-600 text-white">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent className="border-gray-600" style={{ backgroundColor: '#000000' }}>
                    <SelectItem value="x-ai/grok-4" className="text-white hover:bg-gray-800 focus:bg-gray-800">Grok 4 (X.AI)</SelectItem>
                    <SelectItem value="moonshotai/kimi-k2" className="text-white hover:bg-gray-800 focus:bg-gray-800">Kimi K2 (Moonshot)</SelectItem>
                    <SelectItem value="anthropic/claude-sonnet-4" className="text-white hover:bg-gray-800 focus:bg-gray-800">Claude Sonnet 4 (Default)</SelectItem>
                    <SelectItem value="anthropic/claude-3.7-sonnet:thinking" className="text-white hover:bg-gray-800 focus:bg-gray-800">Claude 3.7 Sonnet (Thinking)</SelectItem>
                    <SelectItem value="anthropic/claude-3.7-sonnet" className="text-white hover:bg-gray-800 focus:bg-gray-800">Claude 3.7 Sonnet</SelectItem>
                    <SelectItem value="openai/gpt-4.1" className="text-white hover:bg-gray-800 focus:bg-gray-800">GPT-4.1 (OpenAI)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Mode Toggle */}
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label htmlFor="autonomous-mode" className="text-sm font-medium text-gray-300">
                    Autonomous Mode
                  </Label>
                  <p className="text-xs text-gray-400">
                    {isAutonomousMode ? "AI runs all steps automatically" : "Guided step-by-step planning"}
                  </p>
                </div>
                <Switch
                  id="autonomous-mode"
                  checked={isAutonomousMode}
                  onCheckedChange={setIsAutonomousMode}
                />
              </div>
            </div>
          </DialogContent>
        </Dialog>

        {/* Powered by AP3X - Bottom Right */}
        <div className="fixed bottom-6 right-6 text-xs text-gray-500 font-medium z-50">
          Powered by{' '}
          <span className="text-white font-semibold">AP3</span>
          <span className="text-[#ff2d55] font-semibold" style={{ textShadow: '0 0 4px #ff2d55, 0 0 8px #ff2d55' }}>X</span>
        </div>

        <div className="w-full max-w-2xl text-center space-y-8">
          <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.6 }}>
            <div className="flex items-center justify-center gap-3 mb-6">
              <Image
                src="/AG3NT.png"
                alt="AG3NT"
                width={60}
                height={20}
                className="object-contain"
                style={{ width: 'auto', height: 'auto' }}
              />
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="space-y-6"
          >
            <div className="space-y-4">
              {/* Image Upload Preview */}
              {uploadedImages.length > 0 && (
                <div className="p-3 rounded-lg" style={{ backgroundColor: '#181818' }}>
                  {isProcessingImages && (
                    <div className="flex items-center gap-2 mb-3 text-blue-600">
                      <Loader2 className="w-4 h-4 animate-spin" />
                      <span className="text-sm">Analyzing images with AI...</span>
                    </div>
                  )}
                  <div className="flex flex-wrap gap-2">
                    {uploadedImages.map((image, index) => (
                      <div key={index} className="relative group">
                        <img
                          src={URL.createObjectURL(image)}
                          alt={`Upload ${index + 1}`}
                          className={`w-16 h-16 object-cover rounded border transition-opacity ${
                            isProcessingImages ? 'opacity-50' : 'opacity-100'
                          }`}
                          title={image.name}
                        />
                        {!isProcessingImages && (
                          <button
                            onClick={() => removeImage(index)}
                            className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white rounded-full flex items-center justify-center text-xs hover:bg-red-600 opacity-0 group-hover:opacity-100 transition-opacity"
                          >
                            <X size={12} />
                          </button>
                        )}
                        <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-75 text-white text-xs p-1 rounded-b opacity-0 group-hover:opacity-100 transition-opacity truncate">
                          {image.name}
                        </div>
                      </div>
                    ))}
                  </div>
                  {uploadedImages.length > 0 && !isProcessingImages && (
                    <div className="mt-2 flex items-center justify-between">
                      <div className="text-xs text-gray-600">
                        {uploadedImages.length} image{uploadedImages.length > 1 ? 's' : ''} ready for AI analysis
                      </div>
                      <button
                        onClick={clearAllImages}
                        className="text-xs text-red-500 hover:text-red-700 underline"
                      >
                        Clear all
                      </button>
                    </div>
                  )}
                </div>
              )}

              {/* Input with Paperclip and Send Button */}
              <div className="flex w-full items-end gap-0 rounded-3xl overflow-hidden bg-white shadow-lg border border-gray-100">
                <Button
                  onClick={triggerImageUpload}
                  className="flex-shrink-0 h-12 w-12 text-gray-600 bg-transparent border-0 hover:bg-gray-50 transition-colors duration-200 rounded-l-3xl"
                  title="Upload design reference images for AI analysis"
                  disabled={isProcessing || isProcessingImages}
                >
                  <Paperclip size={18} />
                  <span className="sr-only">Upload Images</span>
                </Button>

                <div className="flex-1 relative flex items-center">
                  <textarea
                    value={userPrompt}
                    onChange={(e) => {
                      setUserPrompt(e.target.value)
                      // Auto-resize logic
                      const textarea = e.target as HTMLTextAreaElement
                      textarea.style.height = 'auto'
                      const newHeight = Math.min(Math.max(textarea.scrollHeight, 48), 240) // 48px min, 240px max (10 lines)
                      textarea.style.height = `${newHeight}px`
                    }}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault()
                        if (userPrompt.trim()) {
                          handleStartPlanning()
                        }
                      }
                    }}
                    placeholder="What would you like to build?"
                    className="w-full h-12 max-h-[240px] py-3 px-4 text-base text-black placeholder-gray-500 bg-transparent border-0 resize-none focus:outline-none focus:ring-0 leading-6 scrollbar-hide"
                    style={{
                      height: '48px'
                    }}
                    autoFocus
                  />
                </div>

                <Button
                  onClick={handleStartPlanning}
                  disabled={!userPrompt.trim()}
                  className="flex-shrink-0 h-12 w-12 text-white bg-[#ff2d55] border-0 hover:bg-[#e6254d] transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                  style={{
                    borderTopRightRadius: '1.5rem',
                    borderBottomRightRadius: '1.5rem',
                    borderTopLeftRadius: '0',
                    borderBottomLeftRadius: '0'
                  }}
                >
                  <span className="sr-only">Start</span>
                  <svg width="20" height="20" fill="none" viewBox="0 0 20 20">
                    <path d="M4 3v14l12-7L4 3z" fill="white"/>
                  </svg>
                </Button>
              </div>

              {/* Hidden File Input */}
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                multiple
                onChange={handleImageUpload}
                className="hidden"
              />
            </div>



            
          </motion.div>


        </div>
      </div>
    )
  }

  return (
    <div className="h-screen w-full bg-[#000000] text-[#e5e5e5] font-sans text-sm flex flex-col overflow-hidden">
      {/* Top Navigation Bar */}
      <header className="h-12 md:h-14 bg-[#0a0a0a] border-b border-[#1a1a1a] flex items-center justify-between px-3 md:px-6">
        <div className="flex items-center gap-3 md:gap-6 min-w-0">
          <div className="flex items-center gap-2 md:gap-3">
            <div className="text-lg md:text-xl font-bold">
              <span className="text-white">AP3</span>
              <span className="text-[#ff2d55]" style={{ textShadow: '0 0 4px #ff2d55, 0 0 8px #ff2d55' }}>X</span>
            </div>
          </div>
          <div className="hidden sm:flex items-center gap-1 text-xs text-[#666]">
            <span>Personal</span>
            <span>/</span>
            <span>Project Planning</span>
          </div>
        </div>
        <div className="flex items-center gap-1 md:gap-2">
          <Dialog open={isSettingsOpen} onOpenChange={setIsSettingsOpen}>
            <DialogTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-7 md:h-8 px-2 md:px-3 text-xs text-[#888] hover:text-white hover:bg-[#1a1a1a] rounded-md"
              >
                <Settings className="w-3 h-3 md:mr-2" />
                <span className="hidden md:inline">Settings</span>
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-md border-gray-700 text-white" style={{ backgroundColor: '#181818' }}>
              <DialogHeader>
                <DialogTitle className="text-white">Settings</DialogTitle>
              </DialogHeader>
              <div className="space-y-6 py-4">
                {/* API Key Input */}
                <div className="space-y-2">
                  <Label htmlFor="api-key" className="text-sm font-medium text-gray-300">
                    OpenRouter API Key
                  </Label>
                  <Input
                    id="api-key"
                    type="password"
                    placeholder="sk-or-v1-..."
                    value={userApiKey}
                    onChange={(e) => setUserApiKey(e.target.value)}
                    className="bg-gray-800 border-gray-600 text-white placeholder-gray-400 focus:border-blue-500"
                  />
                  <p className="text-xs text-gray-400">
                    Optional: Use your own API key for unlimited usage
                  </p>
                </div>

                {/* Model Selection */}
                <div className="space-y-2">
                  <Label htmlFor="model" className="text-sm font-medium text-gray-300">
                    Preferred Model
                  </Label>
                  <Select value={preferredModel} onValueChange={setPreferredModel}>
                    <SelectTrigger className="bg-gray-800 border-gray-600 text-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="border-gray-600" style={{ backgroundColor: '#000000' }}>
                      <SelectItem value="x-ai/grok-4" className="text-white hover:bg-gray-800 focus:bg-gray-800">Grok 4 (X.AI)</SelectItem>
                      <SelectItem value="moonshotai/kimi-k2" className="text-white hover:bg-gray-800 focus:bg-gray-800">Kimi K2 (Moonshot)</SelectItem>
                      <SelectItem value="anthropic/claude-sonnet-4" className="text-white hover:bg-gray-800 focus:bg-gray-800">Claude Sonnet 4 (Default)</SelectItem>
                      <SelectItem value="anthropic/claude-3.7-sonnet:thinking" className="text-white hover:bg-gray-800 focus:bg-gray-800">Claude 3.7 Sonnet (Thinking)</SelectItem>
                      <SelectItem value="anthropic/claude-3.7-sonnet" className="text-white hover:bg-gray-800 focus:bg-gray-800">Claude 3.7 Sonnet</SelectItem>
                      <SelectItem value="openai/gpt-4.1" className="text-white hover:bg-gray-800 focus:bg-gray-800">GPT-4.1 (OpenAI)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Mode Toggle */}
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label htmlFor="autonomous-mode" className="text-sm font-medium text-gray-300">
                      Autonomous Mode
                    </Label>
                    <p className="text-xs text-gray-400">
                      {isAutonomousMode ? "AI runs all steps automatically" : "Guided step-by-step planning"}
                    </p>
                  </div>
                  <Switch
                    id="autonomous-mode"
                    checked={isAutonomousMode}
                    onCheckedChange={setIsAutonomousMode}
                  />
                </div>
              </div>
            </DialogContent>
          </Dialog>
          <Button
            size="sm"
            className="h-7 md:h-8 px-2 md:px-4 bg-white text-black hover:bg-gray-100 text-xs font-medium rounded-md"
          >
            <span className="hidden sm:inline">Publish</span>
            <span className="sm:hidden">📤</span>
          </Button>
        </div>
      </header>



      {/* Main Content Area */}
      <div className="flex flex-1 overflow-hidden p-4 gap-2">
        {/* Left Sidebar - Chat */}
        <aside
          style={{ width: sidebarWidth }}
          className="bg-[#0a0a0a] rounded-xl flex flex-col overflow-hidden relative h-full shadow-xl"
        >
          {/* Chat Header */}
          <div className="px-4 md:px-6 py-3 md:py-4 border-b border-[#1a1a1a] flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <h2 className="font-medium text-white text-sm md:text-base">Chat</h2>
            </div>
          </div>

          {/* Chat Messages */}
          <div className="flex-1 min-h-0 overflow-hidden">
            <div className="px-4 md:px-6 py-3 md:py-4 space-y-3 md:space-y-4 h-full overflow-y-auto">
              {chatMessages.length === 0 ? (
                <div className="text-center text-gray-400 text-sm">
                  Start a conversation to begin planning your project
                </div>
              ) : (
                chatMessages.map((message) => (
                  <div key={message.id} className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}>
                    <div className={`max-w-[75%] p-3 rounded-lg ${
                      message.type === 'user'
                        ? 'bg-[#ff2d55] text-white'
                        : 'bg-[#1a1a1a] text-gray-200'
                    }`}>
                      <div className="text-sm">{message.content}</div>
                      <div className="text-xs opacity-70 mt-1">
                        {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                      </div>
                    </div>
                  </div>
                ))
              )}

              {/* Planning Progress Message - Scrolling Card Format */}
              {(isProcessing || (!isProcessing && hasStarted && tasks.some(t => t.completed))) && (
                <div className="flex justify-start">
                  <div className="max-w-[75%] p-4 rounded-lg bg-[#1a1a1a] text-gray-200">
                    <div className="text-center mb-4">
                      <h3 className="text-lg font-bold text-white">
                        {isProcessing ? (
                          <>
                            <span className="text-white">AG3N</span>
                            <span className="text-[#ff2d55]">T</span>
                            <span className="text-white"> is Planning Your Project</span>
                          </>
                        ) : 'Planning Complete'}
                      </h3>
                    </div>

                    {/* Scrolling Task Container - Shows 3 tasks at a time */}
                    <div
                      ref={taskListRef}
                      className="h-[180px] overflow-y-auto space-y-2 pr-2 planning-scroll"
                      style={{
                        scrollbarWidth: 'thin',
                        scrollbarColor: '#374151 transparent'
                      }}
                    >
                      {tasks.filter((_, originalIndex) => {
                        const maxIndex = isProcessing ? Math.max(currentTaskIndex + 1, 0) : tasks.length
                        return originalIndex <= maxIndex
                      }).map((task, displayIndex) => {
                        const originalIndex = tasks.findIndex(t => t.id === task.id)
                        const isActive = originalIndex === currentTaskIndex && isProcessing
                        const isCompleted = task.completed

                        return (
                          <div
                            key={task.id}
                            data-task-index={originalIndex}
                            className="flex items-center gap-3 py-2"
                          >
                            <div className="w-6 h-6 flex items-center justify-center flex-shrink-0">
                              {isCompleted ? (
                                <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                                  <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                  </svg>
                                </div>
                              ) : isActive ? (
                                <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center animate-pulse">
                                  <div className="w-2 h-2 bg-white rounded-full"></div>
                                </div>
                              ) : (
                                <div className="w-6 h-6 border-2 border-gray-600 rounded-full"></div>
                              )}
                            </div>
                            <span className={`text-sm ${
                              isCompleted ? 'text-white' : isActive ? 'text-white' : 'text-gray-400'
                            }`}>
                              {task.title}
                            </span>
                          </div>
                        )
                      })}
                    </div>

                    {!isProcessing && (
                      <div className="mt-4 pt-3 border-t border-gray-600 text-center">
                        <button
                          onClick={() => setActiveTab("planning")}
                          className="text-sm text-blue-400 hover:text-blue-300 transition-colors"
                        >
                          View detailed plan →
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Coding Progress Message - Scrolling Card Format */}
              {isCoding && (
                <div className="flex justify-start">
                  <div className="max-w-[75%] p-4 rounded-lg bg-[#1a1a1a] text-gray-200">
                    <div className="text-center mb-4">
                      <h3 className="text-lg font-bold text-white">
                        <span className="text-white">AG3N</span>
                        <span className="text-[#ff2d55]">T</span>
                        <span className="text-white"> is Building Your Project</span>
                      </h3>
                      <p className="text-sm text-gray-400 mt-1">
                        Autonomous coding agents are generating your cyberpunk calculator
                      </p>
                    </div>

                    {/* Scrolling Coding Task Container */}
                    <div
                      className="h-[180px] overflow-y-auto space-y-2 pr-2 planning-scroll"
                      style={{
                        scrollbarWidth: 'thin',
                        scrollbarColor: '#374151 transparent'
                      }}
                    >
                      {codingTasks.map((task, index) => {
                        const isActive = task.status === 'in_progress'
                        const isCompleted = task.status === 'completed'

                        return (
                          <div
                            key={task.id}
                            className="flex items-center gap-3 py-2"
                          >
                            <div className="w-6 h-6 flex items-center justify-center flex-shrink-0">
                              {isCompleted ? (
                                <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                                  <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                  </svg>
                                </div>
                              ) : isActive ? (
                                <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center animate-pulse">
                                  <div className="w-2 h-2 bg-white rounded-full"></div>
                                </div>
                              ) : (
                                <div className="w-6 h-6 border-2 border-gray-600 rounded-full"></div>
                              )}
                            </div>
                            <div className="flex-1">
                              <span className={`text-sm ${
                                isCompleted ? 'text-white' : isActive ? 'text-white' : 'text-gray-400'
                              }`}>
                                {task.title}
                              </span>
                              {isActive && activeFile && (
                                <div className="text-xs text-green-400 mt-1 font-mono">
                                  📄 {activeFile}
                                </div>
                              )}
                              {isCompleted && task.output && (
                                <div className="text-xs text-green-400 mt-1">
                                  ✓ {task.output.files?.length || 0} files generated
                                </div>
                              )}
                            </div>
                          </div>
                        )
                      })}
                    </div>

                    {/* Overall Progress Bar */}
                    <div className="mt-4 pt-3 border-t border-gray-600">
                      <div className="flex items-center justify-between text-sm text-gray-300 mb-2">
                        <span>Overall Progress</span>
                        <span>{Math.round((codingTasks.filter(t => t.status === 'completed').length / codingTasks.length) * 100)}%</span>
                      </div>
                      <div className="w-full bg-gray-700 rounded-full h-2">
                        <div
                          className="bg-green-500 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${(codingTasks.filter(t => t.status === 'completed').length / codingTasks.length) * 100}%` }}
                        ></div>
                      </div>
                      <div className="mt-2 text-center">
                        <button
                          onClick={() => setActiveTab("code")}
                          className="text-sm text-green-400 hover:text-green-300 transition-colors"
                        >
                          View live code generation →
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Chat Input */}
          <div className="shrink-0 p-3 md:p-4 border-t border-[#1a1a1a]">
            <div className="relative bg-[#1a1a1a] border border-[#333] rounded-lg">
              {/* Textarea */}
              <textarea
                value={chatInput}
                onChange={(e) => setChatInput(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault()
                    if (chatInput.trim()) {
                      // Add user message
                      const userMessage = {
                        id: Date.now().toString(),
                        type: 'user' as const,
                        content: chatInput,
                        timestamp: new Date()
                      }
                      setChatMessages(prev => [...prev, userMessage])
                      setChatInput("")

                      // Add AI response
                      setTimeout(() => {
                        const aiMessage = {
                          id: (Date.now() + 1).toString(),
                          type: 'ai' as const,
                          content: "I'll help you with that! Let me analyze your request and create a plan.",
                          timestamp: new Date()
                        }
                        setChatMessages(prev => [...prev, aiMessage])
                      }, 500)
                    }
                  }
                }}
                placeholder="Ask a follow-up..."
                className="w-full min-h-[48px] max-h-[144px] p-3 pr-24 text-white placeholder-gray-400 bg-transparent border-0 resize-none focus:outline-none focus:ring-0 leading-6 rounded-lg"
                style={{
                  scrollbarWidth: 'thin',
                  scrollbarColor: '#666 transparent'
                }}
              />

              {/* Action Buttons */}
              <div className="absolute bottom-2 md:bottom-3 right-2 md:right-3 flex items-center gap-1 md:gap-2">
                {/* Attach Button */}
                <button
                  className="w-7 h-7 md:w-8 md:h-8 flex items-center justify-center text-gray-400 hover:text-white transition-colors rounded clickable"
                  title="Attach file"
                >
                  <svg width="14" height="14" className="md:w-4 md:h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                  </svg>
                </button>

                {/* Send Button */}
                <button
                  onClick={() => {
                    if (chatInput.trim()) {
                      // Add user message
                      const userMessage = {
                        id: Date.now().toString(),
                        type: 'user' as const,
                        content: chatInput,
                        timestamp: new Date()
                      }
                      setChatMessages(prev => [...prev, userMessage])
                      setChatInput("")

                      // Add AI response
                      setTimeout(() => {
                        const aiMessage = {
                          id: (Date.now() + 1).toString(),
                          type: 'ai' as const,
                          content: "I'll help you with that! Let me analyze your request and create a plan.",
                          timestamp: new Date()
                        }
                        setChatMessages(prev => [...prev, aiMessage])
                      }, 500)
                    }
                  }}
                  disabled={!chatInput.trim()}
                  className="w-7 h-7 md:w-8 md:h-8 flex items-center justify-center text-gray-400 hover:text-white transition-colors rounded disabled:opacity-50 disabled:cursor-not-allowed clickable"
                  title="Send message"
                >
                  <svg width="14" height="14" className="md:w-4 md:h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                  </svg>
                </button>
              </div>
            </div>
          </div>


        </aside>

        {/* Resize Handle */}
        <div
          ref={resizeRef}
          className={`w-1 bg-transparent hover:bg-[#333] cursor-col-resize transition-colors ${
            isResizing ? 'bg-[#555]' : ''
          }`}
          onMouseDown={handleMouseDown}
        />

        {/* Main Content */}
        <main className="flex-1 bg-[#0a0a0a] rounded-xl flex flex-col overflow-hidden shadow-xl">
          {/* Editor Tabs */}
          <div className="h-12 bg-[#0a0a0a] border-b border-[#1a1a1a] flex items-center px-6">
            <div className="flex items-center gap-1">
              <button
                onClick={() => setActiveTab("preview")}
                className={`flex items-center gap-2 px-3 py-1.5 text-xs rounded-md transition-colors ${
                  activeTab === "preview"
                    ? "bg-[#1a1a1a] text-white"
                    : "text-[#666] hover:text-white hover:bg-[#111111]"
                }`}
              >
                <span>📋</span>
                Preview
              </button>
              <button
                onClick={() => setActiveTab("code")}
                className={`flex items-center gap-2 px-3 py-1.5 text-xs rounded-md transition-colors ${
                  activeTab === "code"
                    ? "bg-[#1a1a1a] text-white"
                    : "text-[#666] hover:text-white hover:bg-[#111111]"
                }`}
              >
                <span>💻</span>
                Code
              </button>
              <button
                onClick={() => setActiveTab("planning")}
                className={`flex items-center gap-2 px-3 py-1.5 text-xs rounded-md transition-colors ${
                  activeTab === "planning"
                    ? "bg-[#1a1a1a] text-white"
                    : "text-[#666] hover:text-white hover:bg-[#111111]"
                }`}
              >
                <span>📝</span>
                Planning
              </button>
              <button
                onClick={() => setActiveTab("graph")}
                className={`flex items-center gap-2 px-3 py-1.5 text-xs rounded-md transition-colors ${
                  activeTab === "graph"
                    ? "bg-[#1a1a1a] text-white"
                    : "text-[#666] hover:text-white hover:bg-[#111111]"
                }`}
              >
                <span>🕸️</span>
                Graph
              </button>
            </div>
          </div>

          {/* Content Area */}
          <div className="flex-1 bg-[#0a0a0a] overflow-hidden">
            {activeTab === "preview" ? (
              // Show Globe component until actual preview is generated (always for now)
              <div className="h-full w-full bg-[#0a0a0a] overflow-hidden relative">
                <Globe key="preview-globe" />
              </div>
            ) : activeTab === "code" ? (
              <div className="h-full flex">
                {/* File Explorer */}
                <div className="w-64 bg-[#0a0a0a] border-r border-[#1a1a1a] flex flex-col">
                  <div className="px-4 py-3 border-b border-[#1a1a1a] flex items-center justify-between">
                    <h3 className="text-sm font-medium text-white">Explorer</h3>
                    {isCoding && (
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    )}
                  </div>
                  <div className="flex-1 p-4 overflow-y-auto planning-scroll">
                    {isCoding ? (
                      <div className="text-xs space-y-1">
                        {/* Dynamic file structure based on coding progress */}
                        <div className="text-[#666] mb-2">cyberpunk-calculator/</div>

                        {/* Source folder */}
                        <div className="ml-2">
                          <div className="text-[#666] mb-1">📁 src/</div>

                          {/* Components */}
                          <div className="ml-4">
                            <div className="text-[#666] mb-1">📁 components/</div>
                            <div className="ml-4 space-y-1">
                              <div
                                className={`p-1 rounded cursor-pointer transition-colors ${
                                  activeFile?.includes('Calculator.tsx') ? 'bg-[#1a1a1a] text-white' : 'text-[#666] hover:text-white hover:bg-[#111111]'
                                }`}
                                onClick={() => setActiveFile('src/components/Calculator.tsx')}
                              >
                                📄 Calculator.tsx
                                {activeFile?.includes('Calculator.tsx') && (
                                  <span className="ml-2 text-green-400 animate-pulse">●</span>
                                )}
                              </div>
                              <div
                                className={`p-1 rounded cursor-pointer transition-colors ${
                                  activeFile?.includes('Display.tsx') ? 'bg-[#1a1a1a] text-white' : 'text-[#666] hover:text-white hover:bg-[#111111]'
                                }`}
                                onClick={() => setActiveFile('src/components/Display.tsx')}
                              >
                                📄 Display.tsx
                              </div>
                              <div
                                className={`p-1 rounded cursor-pointer transition-colors ${
                                  activeFile?.includes('Button.tsx') ? 'bg-[#1a1a1a] text-white' : 'text-[#666] hover:text-white hover:bg-[#111111]'
                                }`}
                                onClick={() => setActiveFile('src/components/Button.tsx')}
                              >
                                📄 Button.tsx
                              </div>
                            </div>
                          </div>

                          {/* Styles */}
                          <div className="ml-4 mt-2">
                            <div className="text-[#666] mb-1">📁 styles/</div>
                            <div className="ml-4 space-y-1">
                              <div
                                className={`p-1 rounded cursor-pointer transition-colors ${
                                  activeFile?.includes('cyberpunk.css') ? 'bg-[#1a1a1a] text-white' : 'text-[#666] hover:text-white hover:bg-[#111111]'
                                }`}
                                onClick={() => setActiveFile('src/styles/cyberpunk.css')}
                              >
                                📄 cyberpunk.css
                                {activeFile?.includes('cyberpunk.css') && (
                                  <span className="ml-2 text-green-400 animate-pulse">●</span>
                                )}
                              </div>
                              <div
                                className={`p-1 rounded cursor-pointer transition-colors ${
                                  activeFile?.includes('neon-effects.css') ? 'bg-[#1a1a1a] text-white' : 'text-[#666] hover:text-white hover:bg-[#111111]'
                                }`}
                                onClick={() => setActiveFile('src/styles/neon-effects.css')}
                              >
                                📄 neon-effects.css
                              </div>
                            </div>
                          </div>

                          {/* Main files */}
                          <div className="ml-4 mt-2 space-y-1">
                            <div
                              className={`p-1 rounded cursor-pointer transition-colors ${
                                activeFile?.includes('App.tsx') ? 'bg-[#1a1a1a] text-white' : 'text-[#666] hover:text-white hover:bg-[#111111]'
                              }`}
                              onClick={() => setActiveFile('src/App.tsx')}
                            >
                              📄 App.tsx
                            </div>
                            <div
                              className={`p-1 rounded cursor-pointer transition-colors ${
                                activeFile?.includes('main.tsx') ? 'bg-[#1a1a1a] text-white' : 'text-[#666] hover:text-white hover:bg-[#111111]'
                              }`}
                              onClick={() => setActiveFile('src/main.tsx')}
                            >
                              📄 main.tsx
                            </div>
                          </div>
                        </div>

                        {/* Database */}
                        <div className="ml-2 mt-2">
                          <div className="text-[#666] mb-1">📁 prisma/</div>
                          <div className="ml-4 space-y-1">
                            <div
                              className={`p-1 rounded cursor-pointer transition-colors ${
                                activeFile?.includes('schema.prisma') ? 'bg-[#1a1a1a] text-white' : 'text-[#666] hover:text-white hover:bg-[#111111]'
                              }`}
                              onClick={() => setActiveFile('prisma/schema.prisma')}
                            >
                              📄 schema.prisma
                              {activeFile?.includes('schema.prisma') && (
                                <span className="ml-2 text-green-400 animate-pulse">●</span>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="text-center text-[#666] py-8">
                        <div className="text-sm">Start coding to see files</div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Code Editor */}
                <div className="flex-1 bg-[#0a0a0a] flex flex-col">
                  {activeFile && isCoding ? (
                    <>
                      {/* File Tab */}
                      <div className="px-4 py-2 border-b border-[#1a1a1a] bg-[#111111]">
                        <div className="flex items-center space-x-2">
                          <span className="text-xs text-white font-mono">{activeFile}</span>
                          {codingTasks.some(task => task.status === 'in_progress') && (
                            <div className="flex items-center space-x-1">
                              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                              <span className="text-xs text-green-400">Live generation</span>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Code Content */}
                      <div className="flex-1 p-4 overflow-auto">
                        <pre className="text-xs text-gray-300 font-mono leading-relaxed">
                          <code>{liveCode || '// Code is being generated...\n// Please wait while the AI agent writes this file'}</code>
                        </pre>
                      </div>
                    </>
                  ) : (
                    <div className="flex-1 flex items-center justify-center">
                      <div className="text-center text-[#666] p-8">
                        <div className="w-12 h-12 mx-auto mb-4 opacity-50">💻</div>
                        {isCoding ? (
                          <>
                            <p className="text-sm">Select a file to view live code generation</p>
                            <p className="text-xs mt-2 opacity-70">Files are being created by AI agents</p>
                          </>
                        ) : (
                          <>
                            <p className="text-sm">Code editor will appear here</p>
                            <p className="text-xs mt-2 opacity-70">Start planning to begin autonomous coding</p>
                          </>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ) : activeTab === "planning" ? (
              // Planning Tab - Show detailed planning results
              <div className="h-full overflow-hidden">
                {!hasStarted ? (
                  <div className="h-full flex items-center justify-center">
                    <div className="text-center text-gray-400">
                      <div className="text-sm">Start planning to see your project breakdown</div>
                    </div>
                  </div>
                ) : !isProcessing && Object.keys(results).length > 0 ? (
                  // Show detailed planning results when complete - Interactive like original
                  <div className="h-full flex overflow-hidden">
                    {/* Left Sidebar - Navigation */}
                    <div className="w-64 bg-[#0a0a0a] border-r border-[#1a1a1a] overflow-y-auto planning-scroll flex-shrink-0">
                      <div className="p-4 border-b border-[#1a1a1a]">
                        <div className="flex items-center gap-2">
                          <h2 className="text-white font-medium">Project Plan</h2>
                          <button
                            onClick={handleExport}
                            className="ml-auto text-gray-400 hover:text-white text-sm hover:bg-[#1a1a1a] px-2 py-1 rounded transition-colors"
                          >
                            Export
                          </button>
                        </div>
                      </div>

                      <div className="p-2">
                        {Object.keys(results).map((key) => {
                          const isSelected = selectedPlanningSection === key
                          const sectionName = key.replace(/([A-Z])/g, ' $1').trim()

                          return (
                            <button
                              key={key}
                              onClick={() => setSelectedPlanningSection(key)}
                              className={`w-full text-left p-3 rounded-lg transition-colors mb-1 ${
                                isSelected
                                  ? 'bg-[#ff2d55] text-white'
                                  : 'hover:bg-[#1a1a1a] text-gray-300'
                              }`}
                            >
                              <div className="flex items-center gap-3">
                                <div className={`w-2 h-2 rounded-full ${
                                  isSelected ? 'bg-white' : 'bg-green-500'
                                }`}></div>
                                <span className="text-sm font-medium capitalize">
                                  {sectionName}
                                </span>
                              </div>
                            </button>
                          )
                        })}
                      </div>
                    </div>

                    {/* Right Content - Selected Section Details */}
                    <div className="flex-1 bg-[#111111] overflow-y-auto planning-scroll max-h-full">
                      {selectedPlanningSection ? (
                        <div className="p-4">
                          <h2 className="text-lg font-semibold text-white mb-4 capitalize">
                            {selectedPlanningSection.replace(/([A-Z])/g, ' $1').trim()}
                          </h2>

                          <div className="space-y-6">
                            {/* Use the complete rendering logic from ResultsView */}
                            {(() => {
                              const content = results[selectedPlanningSection]

                              // Complete rendering functions from ResultsView
                              const renderObjectAsCard = (obj: any, index?: number) => {
                                if (!obj || typeof obj !== "object") return null

                                const hasName = obj.name || obj.title || obj.component || obj.feature || obj.file || obj.folder || obj.path
                                const hasDescription = obj.description || obj.details || obj.content || obj.value || obj.command || obj.script
                                const hasAction = obj.action || obj.task || obj.requirement
                                const hasType = obj.type || obj.env || obj.key

                                if (hasName || hasDescription || hasAction || hasType) {
                                  return (
                                    <div key={index} className="bg-gray-800 p-3 rounded-lg border border-gray-700">
                                      {hasName && (
                                        <div className="text-white font-medium mb-1">
                                          {obj.name || obj.title || obj.component || obj.feature || obj.file || obj.folder || obj.path}
                                        </div>
                                      )}
                                      {hasAction && !hasName && (
                                        <div className="text-white font-medium mb-1">
                                          {obj.action || obj.task || obj.requirement}
                                        </div>
                                      )}
                                      {hasType && !hasName && !hasAction && (
                                        <div className="text-white font-medium mb-1">
                                          {obj.type || obj.env || obj.key}
                                        </div>
                                      )}
                                      {hasDescription && (
                                        <div className="text-gray-300 text-sm mb-2">
                                          {obj.description || obj.details || obj.content || obj.value || obj.command || obj.script}
                                        </div>
                                      )}
                                      {/* Render other properties */}
                                      <div className="space-y-1">
                                        {Object.entries(obj).map(([key, val]) => {
                                          if (['name', 'title', 'component', 'feature', 'description', 'details', 'content', 'action', 'task', 'requirement', 'file', 'folder', 'path', 'type', 'env', 'key', 'value', 'command', 'script'].includes(key)) {
                                            return null
                                          }
                                          return (
                                            <div key={key} className="flex justify-between items-start text-xs">
                                              <span className="text-gray-400 capitalize">{key.replace(/([A-Z])/g, " $1").trim()}:</span>
                                              <span className="text-gray-300 ml-2">
                                                {Array.isArray(val) ? val.join(", ") : String(val)}
                                              </span>
                                            </div>
                                          )
                                        })}
                                      </div>
                                    </div>
                                  )
                                }
                                return null
                              }

                              const renderValue = (value: any): React.ReactNode => {
                                if (Array.isArray(value)) {
                                  // Check if this is an array of objects that can be rendered as cards
                                  const hasCardableObjects = value.some(item =>
                                    typeof item === "object" && item !== null && (
                                      item.name || item.title || item.component || item.feature ||
                                      item.description || item.action || item.task || item.requirement ||
                                      item.role || item.goal || item.service || item.source ||
                                      item.path || item.type || item.value || item.key || item.file ||
                                      item.folder || item.command || item.script || item.env ||
                                      Object.keys(item).length > 0 // Any object with properties
                                    )
                                  )

                                  if (hasCardableObjects) {
                                    return (
                                      <div className="space-y-3">
                                        {value.map((item, index) => {
                                          const cardRender = renderObjectAsCard(item, index)
                                          if (cardRender) return cardRender
                                          return (
                                            <div key={index} className="bg-gray-800 p-3 rounded-lg border border-gray-700">
                                              <div className="text-gray-300 text-sm">
                                                {typeof item === "object" ? JSON.stringify(item, null, 2) : String(item)}
                                              </div>
                                            </div>
                                          )
                                        })}
                                      </div>
                                    )
                                  }

                                  // Default array rendering
                                  return (
                                    <ul className="text-gray-300 space-y-1">
                                      {value.map((item, index) => (
                                        <li key={index} className="flex items-start gap-2">
                                          <span className="text-gray-500 mt-1">•</span>
                                          <div className="flex-1">
                                            {typeof item === "object" ? (
                                              <div className="bg-gray-800 p-3 rounded-lg border border-gray-700">
                                                <pre className="text-gray-300 text-sm whitespace-pre-wrap">
                                                  {JSON.stringify(item, null, 2)}
                                                </pre>
                                              </div>
                                            ) : String(item)}
                                          </div>
                                        </li>
                                      ))}
                                    </ul>
                                  )
                                }

                                if (typeof value === "object" && value !== null) {
                                  // Try to render as a card first
                                  const cardRender = renderObjectAsCard(value)
                                  if (cardRender) return cardRender

                                  // Enhanced object rendering with nested expansion
                                  return (
                                    <div className="space-y-3">
                                      {Object.entries(value).map(([key, val]) => (
                                        <div key={key} className="border-l-2 border-gray-600 pl-3">
                                          <div className="text-gray-400 capitalize font-medium mb-1">
                                            {key.replace(/([A-Z])/g, " $1").trim()}:
                                          </div>
                                          <div className="text-gray-300">
                                            {Array.isArray(val) ? (
                                              <ul className="space-y-1">
                                                {val.map((item, index) => (
                                                  <li key={index} className="flex items-start gap-2">
                                                    <span className="text-blue-400 mt-1">•</span>
                                                    <span>{typeof item === "object" && item !== null ?
                                                      (item.name || item.title || item.description || JSON.stringify(item, null, 2)) :
                                                      String(item)
                                                    }</span>
                                                  </li>
                                                ))}
                                              </ul>
                                            ) : typeof val === "object" && val !== null ? (
                                              <div className="bg-gray-800 p-3 rounded border border-gray-700">
                                                <div className="space-y-2">
                                                  {Object.entries(val).map(([nestedKey, nestedVal]) => (
                                                    <div key={nestedKey} className="flex justify-between items-start">
                                                      <span className="text-gray-400 text-sm capitalize">
                                                        {nestedKey.replace(/([A-Z])/g, " $1").trim()}:
                                                      </span>
                                                      <span className="text-white text-sm ml-2 flex-1 text-right">
                                                        {typeof nestedVal === "object" && nestedVal !== null ?
                                                          JSON.stringify(nestedVal, null, 2) :
                                                          String(nestedVal)
                                                        }
                                                      </span>
                                                    </div>
                                                  ))}
                                                </div>
                                              </div>
                                            ) : (
                                              <span className="text-white">{String(val)}</span>
                                            )}
                                          </div>
                                        </div>
                                      ))}
                                    </div>
                                  )
                                }

                                return <div className="text-gray-300 whitespace-pre-wrap">{String(value)}</div>
                              }

                              // Section-specific rendering logic
                              const renderSectionContent = (sectionId: string, data: any) => {
                                if (sectionId === "wireframes" && data) {
                                  return (
                                    <div className="space-y-6">
                                      {/* Page Wireframes */}
                                      {data.pages && Array.isArray(data.pages) && (
                                        <div>
                                          <h4 className="text-white font-medium mb-4">Page Wireframes</h4>
                                          <div className="space-y-6">
                                            {data.pages.map((page: any, index: number) => (
                                              <div key={index} className="border border-gray-700 rounded-lg p-4">
                                                <div className="flex items-center gap-2 mb-3">
                                                  <h5 className="text-white font-semibold text-lg">{page.name}</h5>
                                                  <span className="text-gray-400 text-sm">{page.type}</span>
                                                </div>
                                                {page.description && (
                                                  <p className="text-gray-300 text-sm mb-4">{page.description}</p>
                                                )}
                                                {(page.layout || page.wireframe) && (
                                                  <div>
                                                    <h6 className="text-white font-medium mb-2">Wireframe</h6>
                                                    <pre className="bg-gray-800 p-4 rounded text-green-400 text-sm font-mono overflow-x-auto whitespace-pre">
                                                      {page.layout || page.wireframe}
                                                    </pre>
                                                  </div>
                                                )}
                                                {page.components && Array.isArray(page.components) && (
                                                  <div className="mt-4">
                                                    <h6 className="text-white font-medium mb-2">Components</h6>
                                                    <div className="grid grid-cols-1 gap-2">
                                                      {page.components.map((comp: any, compIndex: number) => (
                                                        <div key={compIndex} className="bg-gray-800 p-3 rounded">
                                                          <div className="text-white font-medium">{comp.name || comp}</div>
                                                          {comp.description && (
                                                            <div className="text-gray-300 text-sm mt-1">{comp.description}</div>
                                                          )}
                                                        </div>
                                                      ))}
                                                    </div>
                                                  </div>
                                                )}
                                                {page.interactions && Array.isArray(page.interactions) && (
                                                  <div className="mt-4">
                                                    <h6 className="text-white font-medium mb-2">Interactions</h6>
                                                    <ul className="text-gray-300 space-y-1">
                                                      {page.interactions.map((interaction: any, intIndex: number) => (
                                                        <li key={intIndex} className="flex items-start gap-2">
                                                          <span className="text-gray-500 mt-1">•</span>
                                                          <span className="text-sm">{typeof interaction === "object" ? interaction.description || interaction.action : interaction}</span>
                                                        </li>
                                                      ))}
                                                    </ul>
                                                  </div>
                                                )}
                                              </div>
                                            ))}
                                          </div>
                                        </div>
                                      )}

                                      {/* Components */}
                                      {data.components && Array.isArray(data.components) && (
                                        <div>
                                          <h4 className="text-white font-medium mb-4">Components</h4>
                                          <div className="grid grid-cols-1 gap-3">
                                            {data.components.map((comp: any, index: number) => (
                                              <div key={index} className="bg-gray-800 p-4 rounded-lg border border-gray-700">
                                                <div className="flex items-start gap-3">
                                                  <span className="flex-shrink-0 w-6 h-6 bg-red-600 text-white text-xs rounded-full flex items-center justify-center font-bold">
                                                    {index + 1}
                                                  </span>
                                                  <div className="flex-1">
                                                    <div className="text-white font-medium">{comp.name || comp.component}</div>
                                                    {comp.description && (
                                                      <div className="text-gray-300 text-sm mt-1">{comp.description}</div>
                                                    )}
                                                  </div>
                                                </div>
                                              </div>
                                            ))}
                                          </div>
                                        </div>
                                      )}

                                      {/* User Flow */}
                                      {data.userFlow && Array.isArray(data.userFlow) && (
                                        <div>
                                          <h4 className="text-white font-medium mb-3">User Flow</h4>
                                          <div className="space-y-2">
                                            {data.userFlow.map((step: any, index: number) => (
                                              <div key={index} className="flex items-start gap-3 p-3 bg-gray-800 rounded-lg">
                                                <span className="flex-shrink-0 w-6 h-6 bg-red-600 text-white text-xs rounded-full flex items-center justify-center font-bold">
                                                  {step.step || index + 1}
                                                </span>
                                                <div className="flex-1">
                                                  <div className="text-white font-medium">{step.action}</div>
                                                  {step.page && <div className="text-gray-400 text-sm">Page: {step.page}</div>}
                                                  {step.result && <div className="text-gray-300 text-sm mt-1">{step.result}</div>}
                                                </div>
                                              </div>
                                            ))}
                                          </div>
                                        </div>
                                      )}

                                      {/* Other properties */}
                                      {Object.entries(data).map(([key, value]) => {
                                        if (['pages', 'components', 'userFlow', 'mcpEnhanced', 'enrichmentsUsed', 'sequentialThinkingApplied', 'bestPracticesApplied', '_mcpEnhanced', '_enrichmentCount', '_hasSequentialThinking', '_hasBestPractices'].includes(key)) return null
                                        return (
                                          <div key={key}>
                                            <h4 className="text-white font-medium mb-2 capitalize">{key.replace(/([A-Z])/g, " $1").trim()}</h4>
                                            {renderValue(value)}
                                          </div>
                                        )
                                      })}
                                    </div>
                                  )
                                }

                                // Design section rendering
                                if (sectionId === "design" && data) {
                                  return (
                                    <div className="space-y-6">
                                      {/* Theme */}
                                      {data.theme && (
                                        <div>
                                          <h4 className="text-white font-medium mb-4">Theme</h4>
                                          <div className="bg-gray-800 p-4 rounded-lg">
                                            {typeof data.theme === 'object' ? (
                                              <div className="space-y-3">
                                                {Object.entries(data.theme).map(([key, value]) => (
                                                  <div key={key} className="border-l-2 border-blue-500 pl-3">
                                                    <div className="text-gray-300 capitalize font-medium mb-1">
                                                      {key.replace(/([A-Z])/g, " $1").trim()}:
                                                    </div>
                                                    <div className="text-white">
                                                      {typeof value === 'object' && value !== null ? (
                                                        <div className="bg-gray-700 p-2 rounded text-sm">
                                                          {Object.entries(value).map(([nestedKey, nestedVal]) => (
                                                            <div key={nestedKey} className="flex justify-between mb-1">
                                                              <span className="text-gray-300">{nestedKey}:</span>
                                                              <span className="text-white">{String(nestedVal)}</span>
                                                            </div>
                                                          ))}
                                                        </div>
                                                      ) : (
                                                        String(value)
                                                      )}
                                                    </div>
                                                  </div>
                                                ))}
                                              </div>
                                            ) : (
                                              <div className="text-gray-300">{String(data.theme)}</div>
                                            )}
                                          </div>
                                        </div>
                                      )}

                                      {/* Color Palette */}
                                      {data.colorPalette && (
                                        <div>
                                          <h4 className="text-white font-medium mb-4">Color Palette</h4>
                                          <div className="bg-gray-800 p-4 rounded-lg">
                                            {typeof data.colorPalette === 'object' ? (
                                              <div className="space-y-4">
                                                {Object.entries(data.colorPalette).map(([key, value]) => (
                                                  <div key={key} className="border-l-2 border-purple-500 pl-3">
                                                    <div className="text-gray-300 capitalize font-medium mb-2">
                                                      {key.replace(/([A-Z])/g, " $1").trim()}:
                                                    </div>
                                                    {typeof value === 'object' && value !== null ? (
                                                      <div className="space-y-2">
                                                        {Object.entries(value).map(([colorKey, colorValue]) => (
                                                          <div key={colorKey} className="flex items-center justify-between bg-gray-700 p-2 rounded">
                                                            <span className="text-gray-300 text-sm capitalize">{colorKey}:</span>
                                                            <div className="flex items-center gap-2">
                                                              <span className="text-white font-mono text-sm">{String(colorValue)}</span>
                                                              {typeof colorValue === 'string' && colorValue.startsWith('#') && (
                                                                <div
                                                                  className="w-5 h-5 rounded border border-gray-600"
                                                                  style={{ backgroundColor: colorValue }}
                                                                ></div>
                                                              )}
                                                            </div>
                                                          </div>
                                                        ))}
                                                      </div>
                                                    ) : (
                                                      <div className="flex items-center gap-2">
                                                        <span className="text-white font-mono">{String(value)}</span>
                                                        {typeof value === 'string' && value.startsWith('#') && (
                                                          <div
                                                            className="w-6 h-6 rounded border border-gray-600"
                                                            style={{ backgroundColor: value }}
                                                          ></div>
                                                        )}
                                                      </div>
                                                    )}
                                                  </div>
                                                ))}
                                              </div>
                                            ) : (
                                              <div className="text-gray-300">{String(data.colorPalette)}</div>
                                            )}
                                          </div>
                                        </div>
                                      )}

                                      {/* Typography */}
                                      {data.typography && (
                                        <div>
                                          <h4 className="text-white font-medium mb-4">Typography</h4>
                                          <div className="bg-gray-800 p-4 rounded-lg">
                                            {typeof data.typography === 'object' ? (
                                              <div className="space-y-3">
                                                {Object.entries(data.typography).map(([key, value]) => (
                                                  <div key={key} className="border-l-2 border-green-500 pl-3">
                                                    <div className="text-gray-300 capitalize font-medium mb-1">
                                                      {key.replace(/([A-Z])/g, " $1").trim()}:
                                                    </div>
                                                    <div className="text-white">
                                                      {typeof value === 'object' && value !== null ? (
                                                        <div className="bg-gray-700 p-2 rounded text-sm space-y-1">
                                                          {Object.entries(value).map(([nestedKey, nestedVal]) => (
                                                            <div key={nestedKey} className="flex justify-between">
                                                              <span className="text-gray-300">{nestedKey}:</span>
                                                              <span className="text-white font-mono">{String(nestedVal)}</span>
                                                            </div>
                                                          ))}
                                                        </div>
                                                      ) : (
                                                        <span className="font-mono">{String(value)}</span>
                                                      )}
                                                    </div>
                                                  </div>
                                                ))}
                                              </div>
                                            ) : (
                                              <div className="text-gray-300">{String(data.typography)}</div>
                                            )}
                                          </div>
                                        </div>
                                      )}

                                      {/* Layout */}
                                      {data.layout && (
                                        <div>
                                          <h4 className="text-white font-medium mb-4">Layout</h4>
                                          <div className="bg-gray-800 p-4 rounded-lg">
                                            {renderValue(data.layout)}
                                          </div>
                                        </div>
                                      )}

                                      {/* Interactive Elements */}
                                      {data.interactive && (
                                        <div>
                                          <h4 className="text-white font-medium mb-4">Interactive Elements</h4>
                                          <div className="bg-gray-800 p-4 rounded-lg">
                                            {renderValue(data.interactive)}
                                          </div>
                                        </div>
                                      )}

                                      {/* Effects */}
                                      {data.effects && (
                                        <div>
                                          <h4 className="text-white font-medium mb-4">Effects</h4>
                                          <div className="bg-gray-800 p-4 rounded-lg">
                                            {renderValue(data.effects)}
                                          </div>
                                        </div>
                                      )}

                                      {/* Animations */}
                                      {data.animations && (
                                        <div>
                                          <h4 className="text-white font-medium mb-4">Animations</h4>
                                          <div className="bg-gray-800 p-4 rounded-lg">
                                            {renderValue(data.animations)}
                                          </div>
                                        </div>
                                      )}

                                      {/* Other design properties */}
                                      {Object.entries(data).map(([key, value]) => {
                                        if (['theme', 'colorPalette', 'typography', 'layout', 'interactive', 'effects', 'animations', 'mcpEnhanced', 'enrichmentsUsed', 'sequentialThinkingApplied', 'bestPracticesApplied', '_mcpEnhanced', '_enrichmentCount', '_hasSequentialThinking', '_hasBestPractices'].includes(key)) return null
                                        return (
                                          <div key={key}>
                                            <h4 className="text-white font-medium mb-4 capitalize">{key.replace(/([A-Z])/g, " $1").trim()}</h4>
                                            <div className="bg-gray-800 p-4 rounded-lg">
                                              {renderValue(value)}
                                            </div>
                                          </div>
                                        )
                                      })}
                                    </div>
                                  )
                                }

                                // Analyze section rendering
                                if (sectionId === "analyze" && data) {
                                  return (
                                    <div className="space-y-6">
                                      {/* Project Type */}
                                      {data.projectType && (
                                        <div>
                                          <h4 className="text-white font-medium mb-3">Project Type</h4>
                                          <div className="bg-gray-800 p-4 rounded-lg">
                                            <div className="text-gray-300">{data.projectType}</div>
                                          </div>
                                        </div>
                                      )}

                                      {/* Requirements */}
                                      {data.requirements && (
                                        <div>
                                          <h4 className="text-white font-medium mb-3">Requirements</h4>
                                          <div className="bg-gray-800 p-4 rounded-lg">
                                            {Array.isArray(data.requirements) ? (
                                              <ul className="space-y-2">
                                                {data.requirements.map((req: any, index: number) => (
                                                  <li key={index} className="flex items-start gap-2">
                                                    <span className="text-blue-400 mt-1">•</span>
                                                    <span className="text-gray-300">{typeof req === 'object' ? req.description || req.requirement || String(req) : req}</span>
                                                  </li>
                                                ))}
                                              </ul>
                                            ) : (
                                              <div className="text-gray-300">{String(data.requirements)}</div>
                                            )}
                                          </div>
                                        </div>
                                      )}

                                      {/* Goals */}
                                      {data.goals && (
                                        <div>
                                          <h4 className="text-white font-medium mb-3">Goals</h4>
                                          <div className="bg-gray-800 p-4 rounded-lg">
                                            {Array.isArray(data.goals) ? (
                                              <ul className="space-y-2">
                                                {data.goals.map((goal: any, index: number) => (
                                                  <li key={index} className="flex items-start gap-2">
                                                    <span className="text-green-400 mt-1">•</span>
                                                    <span className="text-gray-300">{typeof goal === 'object' ? goal.description || goal.goal || String(goal) : goal}</span>
                                                  </li>
                                                ))}
                                              </ul>
                                            ) : (
                                              <div className="text-gray-300">{String(data.goals)}</div>
                                            )}
                                          </div>
                                        </div>
                                      )}

                                      {/* Scope */}
                                      {data.scope && (
                                        <div>
                                          <h4 className="text-white font-medium mb-3">Scope</h4>
                                          <div className="bg-gray-800 p-4 rounded-lg">
                                            <div className="text-gray-300">{String(data.scope)}</div>
                                          </div>
                                        </div>
                                      )}

                                      {/* Other analyze properties */}
                                      {Object.entries(data).map(([key, value]) => {
                                        if (['projectType', 'requirements', 'goals', 'scope', 'mcpEnhanced', 'enrichmentsUsed', 'sequentialThinkingApplied', 'bestPracticesApplied', '_mcpEnhanced', '_enrichmentCount', '_hasSequentialThinking', '_hasBestPractices'].includes(key)) return null
                                        return (
                                          <div key={key}>
                                            <h4 className="text-white font-medium mb-3 capitalize">{key.replace(/([A-Z])/g, " $1").trim()}</h4>
                                            <div className="bg-gray-800 p-4 rounded-lg">
                                              {renderValue(value)}
                                            </div>
                                          </div>
                                        )
                                      })}
                                    </div>
                                  )
                                }

                                // Clarify section rendering
                                if (sectionId === "clarify" && data) {
                                  return (
                                    <div className="space-y-6">
                                      {/* Questions */}
                                      {data.questions && (
                                        <div>
                                          <h4 className="text-white font-medium mb-3">Questions & Answers</h4>
                                          <div className="space-y-3">
                                            {Array.isArray(data.questions) ? (
                                              data.questions.map((qa: any, index: number) => (
                                                <div key={index} className="bg-gray-800 p-4 rounded-lg">
                                                  <div className="text-blue-400 font-medium mb-2">Q: {qa.question || qa.q || qa}</div>
                                                  {qa.answer && <div className="text-gray-300">A: {qa.answer || qa.a}</div>}
                                                </div>
                                              ))
                                            ) : (
                                              <div className="bg-gray-800 p-4 rounded-lg">
                                                <div className="text-gray-300">{String(data.questions)}</div>
                                              </div>
                                            )}
                                          </div>
                                        </div>
                                      )}

                                      {/* Additional Details */}
                                      {data.details && (
                                        <div>
                                          <h4 className="text-white font-medium mb-3">Additional Details</h4>
                                          <div className="bg-gray-800 p-4 rounded-lg">
                                            {Array.isArray(data.details) ? (
                                              <ul className="space-y-2">
                                                {data.details.map((detail: any, index: number) => (
                                                  <li key={index} className="flex items-start gap-2">
                                                    <span className="text-yellow-400 mt-1">•</span>
                                                    <span className="text-gray-300">{String(detail)}</span>
                                                  </li>
                                                ))}
                                              </ul>
                                            ) : (
                                              <div className="text-gray-300">{String(data.details)}</div>
                                            )}
                                          </div>
                                        </div>
                                      )}

                                      {/* Other clarify properties */}
                                      {Object.entries(data).map(([key, value]) => {
                                        if (['questions', 'details', 'mcpEnhanced', 'enrichmentsUsed', 'sequentialThinkingApplied', 'bestPracticesApplied', '_mcpEnhanced', '_enrichmentCount', '_hasSequentialThinking', '_hasBestPractices'].includes(key)) return null
                                        return (
                                          <div key={key}>
                                            <h4 className="text-white font-medium mb-3 capitalize">{key.replace(/([A-Z])/g, " $1").trim()}</h4>
                                            <div className="bg-gray-800 p-4 rounded-lg">
                                              {renderValue(value)}
                                            </div>
                                          </div>
                                        )
                                      })}
                                    </div>
                                  )
                                }

                                // Summary section rendering
                                if (sectionId === "summary" && data) {
                                  return (
                                    <div className="space-y-6">
                                      {/* Overview */}
                                      {data.overview && (
                                        <div>
                                          <h4 className="text-white font-medium mb-3">Project Overview</h4>
                                          <div className="bg-gray-800 p-4 rounded-lg">
                                            <div className="text-gray-300">{String(data.overview)}</div>
                                          </div>
                                        </div>
                                      )}

                                      {/* Key Features */}
                                      {data.keyFeatures && (
                                        <div>
                                          <h4 className="text-white font-medium mb-3">Key Features</h4>
                                          <div className="bg-gray-800 p-4 rounded-lg">
                                            {Array.isArray(data.keyFeatures) ? (
                                              <ul className="space-y-2">
                                                {data.keyFeatures.map((feature: any, index: number) => (
                                                  <li key={index} className="flex items-start gap-2">
                                                    <span className="text-purple-400 mt-1">•</span>
                                                    <span className="text-gray-300">{String(feature)}</span>
                                                  </li>
                                                ))}
                                              </ul>
                                            ) : (
                                              <div className="text-gray-300">{String(data.keyFeatures)}</div>
                                            )}
                                          </div>
                                        </div>
                                      )}

                                      {/* Target Audience */}
                                      {data.targetAudience && (
                                        <div>
                                          <h4 className="text-white font-medium mb-3">Target Audience</h4>
                                          <div className="bg-gray-800 p-4 rounded-lg">
                                            <div className="text-gray-300">{String(data.targetAudience)}</div>
                                          </div>
                                        </div>
                                      )}

                                      {/* Other summary properties */}
                                      {Object.entries(data).map(([key, value]) => {
                                        if (['overview', 'keyFeatures', 'targetAudience', 'mcpEnhanced', 'enrichmentsUsed', 'sequentialThinkingApplied', 'bestPracticesApplied', '_mcpEnhanced', '_enrichmentCount', '_hasSequentialThinking', '_hasBestPractices'].includes(key)) return null
                                        return (
                                          <div key={key}>
                                            <h4 className="text-white font-medium mb-3 capitalize">{key.replace(/([A-Z])/g, " $1").trim()}</h4>
                                            <div className="bg-gray-800 p-4 rounded-lg">
                                              {renderValue(value)}
                                            </div>
                                          </div>
                                        )
                                      })}
                                    </div>
                                  )
                                }

                                // Techstack section rendering
                                if (sectionId === "techstack" && data) {
                                  return (
                                    <div className="space-y-6">
                                      {/* Frontend */}
                                      {data.frontend && (
                                        <div>
                                          <h4 className="text-white font-medium mb-3">Frontend Technologies</h4>
                                          <div className="bg-gray-800 p-4 rounded-lg">
                                            {Array.isArray(data.frontend) ? (
                                              <div className="grid grid-cols-1 gap-2">
                                                {data.frontend.map((tech: any, index: number) => (
                                                  <div key={index} className="flex items-center gap-2">
                                                    <span className="text-blue-400">▶</span>
                                                    <span className="text-white font-medium">{typeof tech === 'object' ? tech.name || tech.technology : tech}</span>
                                                    {typeof tech === 'object' && tech.version && (
                                                      <span className="text-gray-400 text-sm">v{tech.version}</span>
                                                    )}
                                                  </div>
                                                ))}
                                              </div>
                                            ) : typeof data.frontend === 'object' && data.frontend !== null ? (
                                              <div className="space-y-2">
                                                {Object.entries(data.frontend).map(([key, value]) => (
                                                  <div key={key} className="flex items-center gap-2">
                                                    <span className="text-blue-400">▶</span>
                                                    <span className="text-white font-medium">{key}</span>
                                                    <span className="text-gray-400 text-sm">{String(value)}</span>
                                                  </div>
                                                ))}
                                              </div>
                                            ) : (
                                              <div className="text-gray-300">{String(data.frontend)}</div>
                                            )}
                                          </div>
                                        </div>
                                      )}

                                      {/* Backend */}
                                      {data.backend && (
                                        <div>
                                          <h4 className="text-white font-medium mb-3">Backend Technologies</h4>
                                          <div className="bg-gray-800 p-4 rounded-lg">
                                            {Array.isArray(data.backend) ? (
                                              <div className="grid grid-cols-1 gap-2">
                                                {data.backend.map((tech: any, index: number) => (
                                                  <div key={index} className="flex items-center gap-2">
                                                    <span className="text-green-400">▶</span>
                                                    <span className="text-white font-medium">{typeof tech === 'object' ? tech.name || tech.technology : tech}</span>
                                                    {typeof tech === 'object' && tech.version && (
                                                      <span className="text-gray-400 text-sm">v{tech.version}</span>
                                                    )}
                                                  </div>
                                                ))}
                                              </div>
                                            ) : typeof data.backend === 'object' && data.backend !== null ? (
                                              <div className="space-y-2">
                                                {Object.entries(data.backend).map(([key, value]) => (
                                                  <div key={key} className="flex items-center gap-2">
                                                    <span className="text-green-400">▶</span>
                                                    <span className="text-white font-medium">{key}</span>
                                                    <span className="text-gray-400 text-sm">{String(value)}</span>
                                                  </div>
                                                ))}
                                              </div>
                                            ) : (
                                              <div className="text-gray-300">{String(data.backend)}</div>
                                            )}
                                          </div>
                                        </div>
                                      )}

                                      {/* Database */}
                                      {data.database && (
                                        <div>
                                          <h4 className="text-white font-medium mb-3">Database</h4>
                                          <div className="bg-gray-800 p-4 rounded-lg">
                                            {Array.isArray(data.database) ? (
                                              <div className="grid grid-cols-1 gap-2">
                                                {data.database.map((db: any, index: number) => (
                                                  <div key={index} className="flex items-center gap-2">
                                                    <span className="text-yellow-400">▶</span>
                                                    <span className="text-white font-medium">{typeof db === 'object' ? db.name || db.technology : db}</span>
                                                  </div>
                                                ))}
                                              </div>
                                            ) : (
                                              <div className="text-gray-300">{String(data.database)}</div>
                                            )}
                                          </div>
                                        </div>
                                      )}

                                      {/* Tools */}
                                      {data.tools && (
                                        <div>
                                          <h4 className="text-white font-medium mb-3">Development Tools</h4>
                                          <div className="bg-gray-800 p-4 rounded-lg">
                                            {Array.isArray(data.tools) ? (
                                              <div className="grid grid-cols-1 gap-2">
                                                {data.tools.map((tool: any, index: number) => (
                                                  <div key={index} className="flex items-center gap-2">
                                                    <span className="text-purple-400">▶</span>
                                                    <span className="text-white font-medium">{typeof tool === 'object' ? tool.name || tool.tool : tool}</span>
                                                  </div>
                                                ))}
                                              </div>
                                            ) : (
                                              <div className="text-gray-300">{String(data.tools)}</div>
                                            )}
                                          </div>
                                        </div>
                                      )}

                                      {/* Other techstack properties */}
                                      {Object.entries(data).map(([key, value]) => {
                                        if (['frontend', 'backend', 'database', 'tools', 'mcpEnhanced', 'enrichmentsUsed', 'sequentialThinkingApplied', 'bestPracticesApplied', '_mcpEnhanced', '_enrichmentCount', '_hasSequentialThinking', '_hasBestPractices'].includes(key)) return null
                                        return (
                                          <div key={key}>
                                            <h4 className="text-white font-medium mb-3 capitalize">{key.replace(/([A-Z])/g, " $1").trim()}</h4>
                                            <div className="bg-gray-800 p-4 rounded-lg">
                                              {renderValue(value)}
                                            </div>
                                          </div>
                                        )
                                      })}
                                    </div>
                                  )
                                }

                                // PRD section rendering
                                if (sectionId === "prd" && data) {
                                  return (
                                    <div className="space-y-6">
                                      {/* Features */}
                                      {data.features && (
                                        <div>
                                          <h4 className="text-white font-medium mb-3">Features</h4>
                                          <div className="space-y-3">
                                            {Array.isArray(data.features) ? (
                                              data.features.map((feature: any, index: number) => (
                                                <div key={index} className="bg-gray-800 p-4 rounded-lg">
                                                  <div className="flex items-start gap-3">
                                                    <span className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white text-xs rounded-full flex items-center justify-center font-bold">
                                                      {index + 1}
                                                    </span>
                                                    <div className="flex-1">
                                                      <div className="text-white font-medium">{typeof feature === 'object' ? feature.name || feature.title : feature}</div>
                                                      {typeof feature === 'object' && feature.description && (
                                                        <div className="text-gray-300 text-sm mt-1">{feature.description}</div>
                                                      )}
                                                      {typeof feature === 'object' && feature.priority && (
                                                        <div className="text-yellow-400 text-sm mt-1">Priority: {feature.priority}</div>
                                                      )}
                                                    </div>
                                                  </div>
                                                </div>
                                              ))
                                            ) : (
                                              <div className="bg-gray-800 p-4 rounded-lg">
                                                <div className="text-gray-300">{String(data.features)}</div>
                                              </div>
                                            )}
                                          </div>
                                        </div>
                                      )}



                                      {/* User Stories */}
                                      {data.userStories && (
                                        <div>
                                          <h4 className="text-white font-medium mb-3">User Stories</h4>
                                          <div className="space-y-3">
                                            {Array.isArray(data.userStories) ? (
                                              data.userStories.map((story: any, index: number) => (
                                                <div key={index} className="bg-gray-800 p-4 rounded-lg border border-gray-700">
                                                  <div className="flex items-start gap-3">
                                                    <span className="text-blue-400 mt-1">👤</span>
                                                    <div className="flex-1">
                                                      <div className="text-gray-300 text-sm">{typeof story === 'object' ? story.story || story.description || story.title : story}</div>
                                                      {typeof story === 'object' && story.acceptanceCriteria && (
                                                        <div className="mt-2">
                                                          <div className="text-gray-400 text-xs mb-1">Acceptance Criteria:</div>
                                                          <div className="text-gray-400 text-xs">{story.acceptanceCriteria}</div>
                                                        </div>
                                                      )}
                                                    </div>
                                                  </div>
                                                </div>
                                              ))
                                            ) : typeof data.userStories === 'object' && data.userStories !== null ? (
                                              <div className="space-y-3">
                                                {Object.entries(data.userStories).map(([key, value]) => (
                                                  <div key={key} className="bg-gray-800 p-4 rounded-lg border border-gray-700">
                                                    <div className="flex items-start gap-3">
                                                      <span className="text-blue-400 mt-1">👤</span>
                                                      <div className="flex-1">
                                                        <div className="text-white font-medium text-sm mb-2">{key.replace(/([A-Z])/g, " $1").trim()}</div>
                                                        <div className="text-gray-300 text-sm">
                                                          {Array.isArray(value) ? (
                                                            <ul className="space-y-1">
                                                              {value.map((item: any, idx: number) => (
                                                                <li key={idx} className="flex items-start gap-2">
                                                                  <span className="text-green-400 mt-1">•</span>
                                                                  <span>{typeof item === 'object' ? item.story || item.description || item.title || JSON.stringify(item) : String(item)}</span>
                                                                </li>
                                                              ))}
                                                            </ul>
                                                          ) : typeof value === 'object' && value !== null ? (
                                                            <pre className="text-xs bg-gray-900 p-2 rounded mt-1 overflow-x-auto">{JSON.stringify(value, null, 2)}</pre>
                                                          ) : (
                                                            <span>{String(value)}</span>
                                                          )}
                                                        </div>
                                                      </div>
                                                    </div>
                                                  </div>
                                                ))}
                                              </div>
                                            ) : (
                                              <div className="bg-gray-800 p-4 rounded-lg">
                                                <div className="text-gray-300">{String(data.userStories)}</div>
                                              </div>
                                            )}
                                          </div>
                                        </div>
                                      )}

                                      {/* Other PRD properties */}
                                      {Object.entries(data).map(([key, value]) => {
                                        if (['features', 'timeline', 'userStories', 'mcpEnhanced', 'enrichmentsUsed', 'sequentialThinkingApplied', 'bestPracticesApplied', '_mcpEnhanced', '_enrichmentCount', '_hasSequentialThinking', '_hasBestPractices'].includes(key)) return null
                                        return (
                                          <div key={key}>
                                            <h4 className="text-white font-medium mb-3 capitalize">{key.replace(/([A-Z])/g, " $1").trim()}</h4>
                                            <div className="bg-gray-800 p-4 rounded-lg">
                                              {renderValue(value)}
                                            </div>
                                          </div>
                                        )
                                      })}
                                    </div>
                                  )
                                }

                                // Database section rendering
                                if (sectionId === "database" && data) {
                                  return (
                                    <div className="space-y-6">
                                      {/* Tables */}
                                      {data.tables && (
                                        <div>
                                          <h4 className="text-white font-medium mb-3">Database Tables</h4>
                                          <div className="space-y-3">
                                            {Array.isArray(data.tables) ? (
                                              data.tables.map((table: any, index: number) => (
                                                <div key={index} className="bg-gray-800 p-4 rounded-lg border border-gray-700">
                                                  <div className="text-white font-medium mb-2">{table.name || `Table ${index + 1}`}</div>
                                                  {table.description && (
                                                    <div className="text-gray-300 text-sm mb-3">{table.description}</div>
                                                  )}
                                                  {table.fields && Array.isArray(table.fields) && (
                                                    <div className="space-y-1">
                                                      <div className="text-gray-400 text-sm font-medium">Fields:</div>
                                                      {table.fields.map((field: any, fieldIndex: number) => (
                                                        <div key={fieldIndex} className="flex items-center gap-2 text-sm">
                                                          <span className="text-blue-400">•</span>
                                                          <span className="text-white">{field.name || field}</span>
                                                          {field.type && <span className="text-gray-400">({field.type})</span>}
                                                          {field.required && <span className="text-red-400">*</span>}
                                                        </div>
                                                      ))}
                                                    </div>
                                                  )}
                                                </div>
                                              ))
                                            ) : (
                                              <div className="bg-gray-800 p-4 rounded-lg">
                                                <div className="text-gray-300">{String(data.tables)}</div>
                                              </div>
                                            )}
                                          </div>
                                        </div>
                                      )}

                                      {/* Relationships */}
                                      {data.relationships && (
                                        <div>
                                          <h4 className="text-white font-medium mb-3">Relationships</h4>
                                          <div className="bg-gray-800 p-4 rounded-lg">
                                            {Array.isArray(data.relationships) ? (
                                              <div className="space-y-2">
                                                {data.relationships.map((rel: any, index: number) => (
                                                  <div key={index} className="flex items-center gap-2">
                                                    <span className="text-yellow-400">→</span>
                                                    <span className="text-gray-300">{typeof rel === 'object' ? `${rel.from} → ${rel.to} (${rel.type})` : rel}</span>
                                                  </div>
                                                ))}
                                              </div>
                                            ) : (
                                              <div className="text-gray-300">{String(data.relationships)}</div>
                                            )}
                                          </div>
                                        </div>
                                      )}

                                      {/* Schema */}
                                      {data.schema && (
                                        <div>
                                          <h4 className="text-white font-medium mb-3">Schema</h4>
                                          <div className="bg-gray-800 p-4 rounded-lg">
                                            <pre className="text-green-400 text-sm font-mono overflow-x-auto whitespace-pre">
                                              {typeof data.schema === 'string' ? data.schema : JSON.stringify(data.schema, null, 2)}
                                            </pre>
                                          </div>
                                        </div>
                                      )}

                                      {/* Other database properties */}
                                      {Object.entries(data).map(([key, value]) => {
                                        if (['tables', 'relationships', 'schema', 'mcpEnhanced', 'enrichmentsUsed', 'sequentialThinkingApplied', 'bestPracticesApplied', '_mcpEnhanced', '_enrichmentCount', '_hasSequentialThinking', '_hasBestPractices'].includes(key)) return null
                                        return (
                                          <div key={key}>
                                            <h4 className="text-white font-medium mb-3 capitalize">{key.replace(/([A-Z])/g, " $1").trim()}</h4>
                                            <div className="bg-gray-800 p-4 rounded-lg">
                                              {renderValue(value)}
                                            </div>
                                          </div>
                                        )
                                      })}
                                    </div>
                                  )
                                }

                                // Filesystem section rendering
                                if (sectionId === "filesystem" && data) {
                                  return (
                                    <div className="space-y-6">
                                      {/* Structure */}
                                      {data.structure && (
                                        <div>
                                          <h4 className="text-white font-medium mb-3">File Structure</h4>
                                          <div className="bg-gray-800 p-4 rounded-lg">
                                            <pre className="text-green-400 text-sm font-mono overflow-x-auto whitespace-pre">
                                              {typeof data.structure === 'string' ? data.structure : JSON.stringify(data.structure, null, 2)}
                                            </pre>
                                          </div>
                                        </div>
                                      )}

                                      {/* Folders */}
                                      {data.folders && (
                                        <div>
                                          <h4 className="text-white font-medium mb-3">Key Folders</h4>
                                          <div className="bg-gray-800 p-4 rounded-lg">
                                            {Array.isArray(data.folders) ? (
                                              <div className="space-y-2">
                                                {data.folders.map((folder: any, index: number) => (
                                                  <div key={index} className="flex items-start gap-2">
                                                    <span className="text-blue-400 mt-1">📁</span>
                                                    <div>
                                                      <span className="text-white font-medium">{folder.name || folder}</span>
                                                      {folder.description && <div className="text-gray-300 text-sm">{folder.description}</div>}
                                                    </div>
                                                  </div>
                                                ))}
                                              </div>
                                            ) : typeof data.folders === 'object' && data.folders !== null ? (
                                              <div className="space-y-2">
                                                {Object.entries(data.folders).map(([key, value]) => (
                                                  <div key={key} className="flex items-start gap-2">
                                                    <span className="text-blue-400 mt-1">📁</span>
                                                    <div>
                                                      <span className="text-white font-medium">{key}</span>
                                                      <div className="text-gray-300 text-sm">{String(value)}</div>
                                                    </div>
                                                  </div>
                                                ))}
                                              </div>
                                            ) : (
                                              <div className="text-gray-300">{String(data.folders)}</div>
                                            )}
                                          </div>
                                        </div>
                                      )}

                                      {/* Files */}
                                      {data.files && (
                                        <div>
                                          <h4 className="text-white font-medium mb-3">Key Files</h4>
                                          <div className="bg-gray-800 p-4 rounded-lg">
                                            {Array.isArray(data.files) ? (
                                              <div className="space-y-2">
                                                {data.files.map((file: any, index: number) => (
                                                  <div key={index} className="flex items-start gap-2">
                                                    <span className="text-green-400 mt-1">📄</span>
                                                    <div>
                                                      <span className="text-white font-medium">{file.name || file}</span>
                                                      {file.description && <div className="text-gray-300 text-sm">{file.description}</div>}
                                                    </div>
                                                  </div>
                                                ))}
                                              </div>
                                            ) : typeof data.files === 'object' && data.files !== null ? (
                                              <div className="space-y-2">
                                                {Object.entries(data.files).map(([key, value]) => (
                                                  <div key={key} className="flex items-start gap-2">
                                                    <span className="text-green-400 mt-1">📄</span>
                                                    <div>
                                                      <span className="text-white font-medium">{key}</span>
                                                      <div className="text-gray-300 text-sm">{String(value)}</div>
                                                    </div>
                                                  </div>
                                                ))}
                                              </div>
                                            ) : (
                                              <div className="text-gray-300">{String(data.files)}</div>
                                            )}
                                          </div>
                                        </div>
                                      )}

                                      {/* Other filesystem properties */}
                                      {Object.entries(data).map(([key, value]) => {
                                        if (['structure', 'folders', 'files', 'mcpEnhanced', 'enrichmentsUsed', 'sequentialThinkingApplied', 'bestPracticesApplied', '_mcpEnhanced', '_enrichmentCount', '_hasSequentialThinking', '_hasBestPractices'].includes(key)) return null
                                        return (
                                          <div key={key}>
                                            <h4 className="text-white font-medium mb-3 capitalize">{key.replace(/([A-Z])/g, " $1").trim()}</h4>
                                            <div className="bg-gray-800 p-4 rounded-lg">
                                              {renderValue(value)}
                                            </div>
                                          </div>
                                        )
                                      })}
                                    </div>
                                  )
                                }

                                // Workflow section rendering
                                if (sectionId === "workflow" && data) {
                                  return (
                                    <div className="space-y-6">
                                      {/* Steps */}
                                      {data.steps && (
                                        <div>
                                          <h4 className="text-white font-medium mb-3">Workflow Steps</h4>
                                          <div className="space-y-3">
                                            {Array.isArray(data.steps) ? (
                                              data.steps.map((step: any, index: number) => (
                                                <div key={index} className="bg-gray-800 p-4 rounded-lg">
                                                  <div className="flex items-start gap-3">
                                                    <span className="flex-shrink-0 w-6 h-6 bg-purple-600 text-white text-xs rounded-full flex items-center justify-center font-bold">
                                                      {step.step || index + 1}
                                                    </span>
                                                    <div className="flex-1">
                                                      <div className="text-white font-medium">{step.name || step.title || step.action || step}</div>
                                                      {step.description && <div className="text-gray-300 text-sm mt-1">{step.description}</div>}
                                                    </div>
                                                  </div>
                                                </div>
                                              ))
                                            ) : (
                                              <div className="bg-gray-800 p-4 rounded-lg">
                                                <div className="text-gray-300">{String(data.steps)}</div>
                                              </div>
                                            )}
                                          </div>
                                        </div>
                                      )}

                                      {/* Processes */}
                                      {data.processes && (
                                        <div>
                                          <h4 className="text-white font-medium mb-3">Processes</h4>
                                          <div className="space-y-2">
                                            {Array.isArray(data.processes) ? (
                                              data.processes.map((process: any, index: number) => (
                                                <div key={index} className="bg-gray-800 p-3 rounded-lg">
                                                  <div className="text-white font-medium">{process.name || process}</div>
                                                  {process.description && <div className="text-gray-300 text-sm mt-1">{process.description}</div>}
                                                </div>
                                              ))
                                            ) : (
                                              <div className="bg-gray-800 p-4 rounded-lg">
                                                <div className="text-gray-300">{String(data.processes)}</div>
                                              </div>
                                            )}
                                          </div>
                                        </div>
                                      )}

                                      {/* Other workflow properties */}
                                      {Object.entries(data).map(([key, value]) => {
                                        if (['steps', 'processes', 'mcpEnhanced', 'enrichmentsUsed', 'sequentialThinkingApplied', 'bestPracticesApplied', '_mcpEnhanced', '_enrichmentCount', '_hasSequentialThinking', '_hasBestPractices'].includes(key)) return null
                                        return (
                                          <div key={key}>
                                            <h4 className="text-white font-medium mb-3 capitalize">{key.replace(/([A-Z])/g, " $1").trim()}</h4>
                                            <div className="bg-gray-800 p-4 rounded-lg">
                                              {renderValue(value)}
                                            </div>
                                          </div>
                                        )
                                      })}
                                    </div>
                                  )
                                }

                                // Tasks section rendering
                                if (sectionId === "tasks" && data) {
                                  return (
                                    <div className="space-y-6">
                                      {/* Implementation Tasks */}
                                      {data.implementation && (
                                        <div>
                                          <h4 className="text-white font-medium mb-3">Implementation Tasks</h4>
                                          <div className="space-y-3">
                                            {Array.isArray(data.implementation) ? (
                                              data.implementation.map((task: any, index: number) => (
                                                <div key={index} className="bg-gray-800 p-4 rounded-lg border border-gray-700">
                                                  <div className="flex items-start gap-3">
                                                    <span className="flex-shrink-0 w-6 h-6 bg-orange-600 text-white text-xs rounded-full flex items-center justify-center font-bold">
                                                      {index + 1}
                                                    </span>
                                                    <div className="flex-1">
                                                      <div className="text-white font-medium">{task.name || task.title || task.task || task}</div>
                                                      {task.description && <div className="text-gray-300 text-sm mt-1">{task.description}</div>}
                                                      {task.priority && <div className="text-yellow-400 text-sm mt-1">Priority: {task.priority}</div>}
                                                    </div>
                                                  </div>
                                                </div>
                                              ))
                                            ) : (
                                              <div className="bg-gray-800 p-4 rounded-lg">
                                                <div className="text-gray-300">{String(data.implementation)}</div>
                                              </div>
                                            )}
                                          </div>
                                        </div>
                                      )}

                                      {/* Categories */}
                                      {data.categories && (
                                        <div>
                                          <h4 className="text-white font-medium mb-3">Task Categories</h4>
                                          <div className="space-y-3">
                                            {typeof data.categories === 'object' ? (
                                              Object.entries(data.categories).map(([category, tasks]) => (
                                                <div key={category} className="bg-gray-800 p-4 rounded-lg">
                                                  <div className="text-white font-medium mb-2 capitalize">{category.replace(/([A-Z])/g, " $1").trim()}</div>
                                                  {Array.isArray(tasks) ? (
                                                    <ul className="space-y-1">
                                                      {(tasks as any[]).map((task: any, index: number) => (
                                                        <li key={index} className="flex items-start gap-2">
                                                          <span className="text-blue-400 mt-1">•</span>
                                                          <span className="text-gray-300 text-sm">{typeof task === 'object' ? task.name || task.task : task}</span>
                                                        </li>
                                                      ))}
                                                    </ul>
                                                  ) : typeof tasks === 'object' && tasks !== null ? (
                                                    <div className="space-y-2">
                                                      {Object.entries(tasks).map(([key, value]) => (
                                                        <div key={key} className="flex items-start gap-2">
                                                          <span className="text-blue-400 mt-1">▶</span>
                                                          <div className="flex-1">
                                                            <span className="text-white font-medium text-sm">{key.replace(/([A-Z])/g, " $1").trim()}:</span>
                                                            <div className="text-gray-300 text-sm mt-1">
                                                              {Array.isArray(value) ? (
                                                                <ul className="space-y-1 ml-2">
                                                                  {value.map((item: any, idx: number) => (
                                                                    <li key={idx} className="flex items-start gap-2">
                                                                      <span className="text-green-400 mt-1">•</span>
                                                                      <span>{typeof item === 'object' ? item.name || item.task || JSON.stringify(item) : String(item)}</span>
                                                                    </li>
                                                                  ))}
                                                                </ul>
                                                              ) : typeof value === 'object' && value !== null ? (
                                                                <pre className="text-xs bg-gray-900 p-2 rounded mt-1 overflow-x-auto">{JSON.stringify(value, null, 2)}</pre>
                                                              ) : (
                                                                <span>{String(value)}</span>
                                                              )}
                                                            </div>
                                                          </div>
                                                        </div>
                                                      ))}
                                                    </div>
                                                  ) : (
                                                    <div className="text-gray-300 text-sm">{String(tasks)}</div>
                                                  )}
                                                </div>
                                              ))
                                            ) : (
                                              <div className="bg-gray-800 p-4 rounded-lg">
                                                <div className="text-gray-300">{String(data.categories)}</div>
                                              </div>
                                            )}
                                          </div>
                                        </div>
                                      )}

                                      {/* Other tasks properties */}
                                      {Object.entries(data).map(([key, value]) => {
                                        if (['implementation', 'categories', 'mcpEnhanced', 'enrichmentsUsed', 'sequentialThinkingApplied', 'bestPracticesApplied', '_mcpEnhanced', '_enrichmentCount', '_hasSequentialThinking', '_hasBestPractices'].includes(key)) return null
                                        return (
                                          <div key={key}>
                                            <h4 className="text-white font-medium mb-3 capitalize">{key.replace(/([A-Z])/g, " $1").trim()}</h4>
                                            <div className="bg-gray-800 p-4 rounded-lg">
                                              {renderValue(value)}
                                            </div>
                                          </div>
                                        )
                                      })}
                                    </div>
                                  )
                                }

                                // Scaffold section rendering
                                if (sectionId === "scaffold" && data) {
                                  return (
                                    <div className="space-y-6">
                                      <div className="bg-gradient-to-r from-green-900/20 to-blue-900/20 p-4 rounded-lg border border-green-500/30">
                                        <h3 className="text-green-400 font-semibold text-lg mb-2">🚀 Generated Project Scaffold</h3>
                                        <p className="text-gray-300 text-sm">Complete code files and setup commands ready for implementation</p>
                                      </div>

                                      {/* Generated Files */}
                                      {data.files && (
                                        <div>
                                          <h4 className="text-white font-semibold mb-4 text-lg">📁 Generated Files</h4>
                                          <div className="space-y-3">
                                            {Array.isArray(data.files) ? (
                                              data.files.map((file: any, index: number) => (
                                                <div key={index} className="bg-gray-800 p-4 rounded-lg border border-gray-600">
                                                  <div className="flex items-start gap-3">
                                                    <span className="text-green-400 mt-1 text-lg">📄</span>
                                                    <div className="flex-1">
                                                      <div className="text-white font-semibold text-lg">{file.path || file.name || file}</div>
                                                      {file.description && <div className="text-gray-300 text-sm mt-1">{file.description}</div>}
                                                      {file.content && (
                                                        <div className="mt-3">
                                                          <div className="text-gray-400 text-sm mb-2 font-medium">Generated Code:</div>
                                                          <pre className="bg-gray-900 p-4 rounded-lg text-green-400 text-sm font-mono overflow-x-auto max-h-96 border border-gray-700">
                                                            {typeof file.content === 'string' ? file.content : JSON.stringify(file.content, null, 2)}
                                                          </pre>
                                                        </div>
                                                      )}
                                                    </div>
                                                  </div>
                                                </div>
                                              ))
                                            ) : (
                                              <div className="bg-gray-800 p-4 rounded-lg">
                                                <div className="text-gray-300">{String(data.files)}</div>
                                              </div>
                                            )}
                                          </div>
                                        </div>
                                      )}

                                      {/* Commands */}
                                      {data.commands && (
                                        <div>
                                          <h4 className="text-white font-semibold mb-4 text-lg">⚡ Setup Commands</h4>
                                          <div className="bg-gray-800 p-4 rounded-lg">
                                            {Array.isArray(data.commands) ? (
                                              <div className="space-y-2">
                                                {data.commands.map((cmd: any, index: number) => (
                                                  <div key={index} className="flex items-center gap-2">
                                                    <span className="text-blue-400">$</span>
                                                    <code className="text-green-400 font-mono text-sm">{typeof cmd === 'object' ? cmd.command || cmd : cmd}</code>
                                                  </div>
                                                ))}
                                              </div>
                                            ) : (
                                              <div className="text-gray-300">{String(data.commands)}</div>
                                            )}
                                          </div>
                                        </div>
                                      )}

                                      {/* Other scaffold properties */}
                                      {Object.entries(data).map(([key, value]) => {
                                        if (['files', 'commands', 'mcpEnhanced', 'enrichmentsUsed', 'sequentialThinkingApplied', 'bestPracticesApplied', '_mcpEnhanced', '_enrichmentCount', '_hasSequentialThinking', '_hasBestPractices'].includes(key)) return null
                                        return (
                                          <div key={key}>
                                            <h4 className="text-white font-medium mb-3 capitalize">{key.replace(/([A-Z])/g, " $1").trim()}</h4>
                                            <div className="bg-gray-800 p-4 rounded-lg">
                                              {renderValue(value)}
                                            </div>
                                          </div>
                                        )
                                      })}
                                    </div>
                                  )
                                }

                                // Generic section rendering for any unhandled sections
                                if (data && typeof data === 'object') {
                                  return (
                                    <div className="space-y-6">
                                      {Object.entries(data).map(([key, value]) => {
                                        // Filter out MCP-related metadata
                                        if (['mcpEnhanced', 'enrichmentsUsed', 'sequentialThinkingApplied', 'bestPracticesApplied', '_mcpEnhanced', '_enrichmentCount', '_hasSequentialThinking', '_hasBestPractices'].includes(key)) return null

                                        return (
                                          <div key={key}>
                                            <h4 className="text-white font-medium mb-3 capitalize">{key.replace(/([A-Z])/g, " $1").trim()}</h4>
                                            <div className="bg-gray-800 p-4 rounded-lg">
                                              {renderValue(value)}
                                            </div>
                                          </div>
                                        )
                                      })}
                                    </div>
                                  )
                                }

                                // Default rendering for other sections
                                return renderValue(data)
                              }

                              // Try to parse as JSON first
                              try {
                                if (typeof content === 'string') {
                                  const parsed = JSON.parse(content)
                                  return (
                                    <div className="bg-gray-900/50 rounded-lg p-6">
                                      {renderSectionContent(selectedPlanningSection, parsed)}
                                    </div>
                                  )
                                }
                              } catch (e) {
                                // If not valid JSON, display as formatted text
                              }

                              // Display the content using section-specific rendering
                              return (
                                <div className="bg-gray-900/50 rounded-lg p-6">
                                  {renderSectionContent(selectedPlanningSection, content)}
                                </div>
                              )
                            })()}
                          </div>
                        </div>
                      ) : (
                        <div className="h-full flex items-center justify-center">
                          <div className="text-center text-gray-400">
                            <div className="text-lg mb-2">📋</div>
                            <div className="text-sm">Select a section to view details</div>
                            <div className="text-xs mt-2 opacity-70">Click on any section in the sidebar to see its content</div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                ) : (
                  // Show placeholder during planning or if no results
                  <div className="h-full flex items-center justify-center p-6">
                    <div className="text-center text-gray-400">
                      <div className="text-sm">
                        {isProcessing ? 'Planning in progress...' : 'Planning details will appear here'}
                      </div>
                      <div className="text-xs mt-2 opacity-70">
                        {isProcessing ? 'Check the chat for live progress' : 'Detailed planning results will be shown in this tab'}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ) : activeTab === "graph" ? (
              // Graph Tab - Neo4j Visualization
              <div className="h-full overflow-hidden">
                <div className="h-full flex items-center justify-center p-6">
                  <div className="text-center text-gray-400 max-w-2xl">
                    <div className="w-16 h-16 mx-auto mb-6 opacity-50">🕸️</div>
                    <h3 className="text-lg font-medium text-white mb-4">Project Knowledge Graph</h3>
                    <div className="text-sm mb-6">
                      Interactive visualization of your project's context and relationships powered by Neo4j and Graphiti.
                    </div>
                    <div className="bg-[#111111] border border-[#1a1a1a] rounded-lg p-6 text-left">
                      <h4 className="text-white font-medium mb-3">🚧 Coming Soon</h4>
                      <div className="space-y-2 text-sm">
                        <p>• Real-time Neo4j graph visualization using Zep Graph Visualization</p>
                        <p>• Interactive exploration of project entities and relationships</p>
                        <p>• Context-aware insights from your project's knowledge graph</p>
                        <p>• Dynamic updates as your project evolves</p>
                      </div>
                      <div className="mt-4 pt-4 border-t border-[#1a1a1a]">
                        <p className="text-xs text-gray-500">
                          This visualization will integrate with our Context Engine to provide deep insights into your project's structure and dependencies.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ) : null}
          </div>
        </main>
      </div>



    </div>
  )
}
