{"compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "node", "lib": ["ES2022", "DOM"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "noImplicitAny": true, "noImplicitReturns": true, "noImplicitThis": true, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": false, "noUncheckedIndexedAccess": false, "allowUnusedLabels": false, "allowUnreachableCode": false, "resolveJsonModule": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@/core/*": ["src/core/*"], "@/agents/*": ["src/agents/*"], "@/workflows/*": ["src/workflows/*"], "@/coordination/*": ["src/coordination/*"], "@/discovery/*": ["src/discovery/*"], "@/advanced/*": ["src/advanced/*"]}}, "include": ["src/**/*", "examples/**/*", "benchmarks/**/*"], "exclude": ["node_modules", "dist", "coverage", "**/*.test.ts", "**/*.spec.ts"], "ts-node": {"esm": true, "experimentalSpecifierResolution": "node"}}