/**
 * AG3NT Framework - Main Export
 * 
 * The AG3NT Framework - A sophisticated autonomous agent framework that rivals
 * CrewAI and LangGraph with superior capabilities:
 * 
 * 🏆 Superior Features:
 * - MCP-enhanced agents with real-time context
 * - Sequential thinking capabilities
 * - Advanced context enrichment pipeline
 * - Built-in agent registry and coordination
 * - Sophisticated progress tracking
 * - Dynamic workflow orchestration
 * 
 * 🎯 Built for Autonomous Development:
 * - Extracted from proven TypeScript implementation
 * - Production-ready patterns and error handling
 * - Extensible architecture for custom agents
 * - Full observability and monitoring
 * 
 * 💰 Strategic Value:
 * - Framework + Platform = Complete ecosystem
 * - Licensing opportunities for enterprise
 * - Pioneer in MCP-enhanced agent frameworks
 * - Higher valuation than tool-only companies
 */

// Core Framework
export { default as AG3NTFramework, ag3ntFramework } from './ag3nt-framework'
export type { FrameworkConfig, SessionConfig, ExecutionResult } from './ag3nt-framework'

// Base Agent Classes
export { BaseAgent } from './core/base-agent'
export type { 
  AgentState, 
  AgentQuestion, 
  AgentMetadata, 
  AgentCapabilities, 
  AgentConfig,
  MCPContext 
} from './core/base-agent'

// Agent Registry
export { AgentRegistry } from './core/agent-registry'
export type {
  RegisteredAgent,
  AgentStatus,
  AgentRegistrationMetadata,
  AgentHealthCheck,
  AgentQuery
} from './core/agent-registry'

// Agent Communication Protocol
export { AgentCommunicationProtocol } from './core/agent-communication'
export type {
  AgentMessage,
  MessageType,
  MessagePriority,
  CommunicationChannel,
  ChannelType,
  AgentPresence,
  PresenceStatus,
  CoordinationRequest,
  CoordinationType,
  MessageHandler,
  ChannelHandler
} from './core/agent-communication'

// Workflow Coordinator
export { WorkflowCoordinator } from './core/workflow-coordinator'
export type {
  WorkflowDefinition,
  WorkflowStep,
  WorkflowExecution,
  WorkflowStatus,
  WorkflowStepExecution,
  WorkflowStepStatus
} from './core/workflow-coordinator'

// Framework Core API
export * from './core/framework-api'
export { FrameworkAPI } from './core/framework-api-impl'
export { SimpleAPI, createSimpleAPI, quickStart, quickPlan } from './core/simple-api'

// Context Engine
export { UnifiedContextEngine, unifiedContextEngine } from './context/unified-context-engine'
export type {
  AgentContextScope,
  ProjectContext,
  CodebaseContext,
  FileContext,
  SymbolContext,
  ContextEnrichment,
  EnhancedContext,
  UnifiedContextEngineConfig
} from './context/unified-context-engine'

// Built-in Agents
export { default as PlanningAgent } from './agents/planning-agent'
export type { PlanningInput, PlanningResult } from './agents/planning-agent'

export { default as TaskPlannerAgent } from './agents/task-planner-agent'
export type { TaskPlannerInput, TaskPlannerResult } from './agents/task-planner-agent'

export { default as ExecutorAgent } from './agents/executor-agent'
export type { ExecutorInput, ExecutorResult } from './agents/executor-agent'

export { default as FrontendCoderAgent } from './agents/frontend-coder-agent'
export type { FrontendCoderInput, FrontendCoderResult } from './agents/frontend-coder-agent'

export { default as BackendCoderAgent } from './agents/backend-coder-agent'
export type { BackendCoderInput, BackendCoderResult } from './agents/backend-coder-agent'

export { default as TesterAgent } from './agents/tester-agent'
export type { TesterInput, TesterResult } from './agents/tester-agent'

export { default as ReviewerAgent } from './agents/reviewer-agent'
export type { ReviewerInput, ReviewerResult } from './agents/reviewer-agent'

export { default as WorkflowAgent } from './agents/workflow-agent'
export type { WorkflowInput, WorkflowResult } from './agents/workflow-agent'

export { default as ContextEngineAgent } from './agents/context-engine-agent'
export type { ContextEngineInput, ContextEngineResult } from './agents/context-engine-agent'

export { default as DocumentationAgent } from './agents/documentation-agent'
export type { DocumentationInput, DocumentationResult } from './agents/documentation-agent'

export { default as DevOpsAgent } from './agents/devops-agent'
export type { DevOpsInput, DevOpsResult } from './agents/devops-agent'

export { default as SecurityAgent } from './agents/security-agent'
export type { SecurityInput, SecurityResult } from './agents/security-agent'

export { default as MaintenanceAgent } from './agents/maintenance-agent'
export type { MaintenanceInput, MaintenanceResult } from './agents/maintenance-agent'

export { default as IntegrationAgent } from './agents/integration-agent'
export type { IntegrationInput, IntegrationResult } from './agents/integration-agent'

export { default as AnalyticsAgent } from './agents/analytics-agent'
export type { AnalyticsInput, AnalyticsResult } from './agents/analytics-agent'

export { default as UserInteractionAgent } from './agents/user-interaction-agent'
export type { UserInteractionInput, UserInteractionResult } from './agents/user-interaction-agent'

// Coordination Systems
export { TaskDelegationSystem } from './coordination/task-delegation-system'
export { ConsensusProtocolEngine } from './coordination/consensus-protocol-engine'
export { WorkflowHandoffManager } from './coordination/workflow-handoff-manager'
export { CoordinationPatternRegistry } from './coordination/coordination-pattern-registry'

// Advanced Features
export * from './advanced'
export { default as AdvancedFeaturesManager } from './advanced'

// Multi-Agent Workflows
export { MultiAgentWorkflows, CompleteProjectWorkflow, FrontendDevelopmentWorkflow, BackendDevelopmentWorkflow, FeatureDevelopmentWorkflow, BugFixWorkflow } from './workflows/multi-agent-workflows'
export { default as WorkflowOrchestrator } from './workflows/workflow-orchestrator'
export type { WorkflowOrchestratorConfig, WorkflowExecutionContext } from './workflows/workflow-orchestrator'

// Advanced Workflow System
export { default as AdvancedWorkflowEngine } from './workflows/advanced-workflow-engine'
export type { AdvancedWorkflowConfig, WorkflowExecutionContext as AdvancedWorkflowExecutionContext } from './workflows/advanced-workflow-engine'

// Workflow Templates
export { WorkflowTemplates, WebApplicationTemplate, MicroserviceTemplate, EmergencyResponseTemplate, MaintenanceTemplate } from './workflows/workflow-templates'
export type { WorkflowTemplate, TemplateParameter, UsageExample } from './workflows/workflow-templates'

// Workflow Analytics
export { default as WorkflowAnalytics } from './workflows/workflow-analytics'
export type { WorkflowAnalyticsConfig, WorkflowMetrics, WorkflowInsights } from './workflows/workflow-analytics'

// Agent Discovery & Load Balancing
export { default as AgentDiscoveryService } from './discovery/agent-discovery-service'
export type { AgentDiscoveryConfig, AgentInstance, DiscoveryQuery, DiscoveryResult } from './discovery/agent-discovery-service'

export { default as LoadBalancer } from './discovery/load-balancer'
export type { LoadBalancerConfig, LoadBalancingRequest, LoadBalancingResult, LoadDistribution } from './discovery/load-balancer'

export { default as FailoverManager } from './discovery/failover-manager'
export type { FailoverConfig, FailoverEvent, FailoverPlan, FailoverMetrics } from './discovery/failover-manager'

// Import the classes for convenience exports
import AG3NTFrameworkClass from './ag3nt-framework'
import { FrameworkAPI } from './core/framework-api-impl'
import PlanningAgent from './agents/planning-agent'
import TaskPlannerAgent from './agents/task-planner-agent'
import ExecutorAgent from './agents/executor-agent'
import FrontendCoderAgent from './agents/frontend-coder-agent'
import BackendCoderAgent from './agents/backend-coder-agent'
import TesterAgent from './agents/tester-agent'
import ReviewerAgent from './agents/reviewer-agent'
import WorkflowAgent from './agents/workflow-agent'
import ContextEngineAgent from './agents/context-engine-agent'
import DocumentationAgent from './agents/documentation-agent'
import DevOpsAgent from './agents/devops-agent'
import SecurityAgent from './agents/security-agent'
import MaintenanceAgent from './agents/maintenance-agent'
import IntegrationAgent from './agents/integration-agent'
import AnalyticsAgent from './agents/analytics-agent'
import UserInteractionAgent from './agents/user-interaction-agent'
import WorkflowOrchestrator from './workflows/workflow-orchestrator'
import AdvancedWorkflowEngine from './workflows/advanced-workflow-engine'
import WorkflowAnalytics from './workflows/workflow-analytics'
import AgentDiscoveryService from './discovery/agent-discovery-service'
import LoadBalancer from './discovery/load-balancer'
import FailoverManager from './discovery/failover-manager'

// Convenience exports for quick setup
export const createFramework = (config?: any) => new AG3NTFrameworkClass(config)
export const createPlanningAgent = (config?: any) => new PlanningAgent(config)
export const createTaskPlannerAgent = (config?: any) => new TaskPlannerAgent(config)
export const createExecutorAgent = (config?: any) => new ExecutorAgent(config)
export const createFrontendCoderAgent = (config?: any) => new FrontendCoderAgent(config)
export const createBackendCoderAgent = (config?: any) => new BackendCoderAgent(config)
export const createTesterAgent = (config?: any) => new TesterAgent(config)
export const createReviewerAgent = (config?: any) => new ReviewerAgent(config)
export const createWorkflowAgent = (config?: any) => new WorkflowAgent(config)
export const createContextEngineAgent = (config?: any) => new ContextEngineAgent(config)
export const createDocumentationAgent = (config?: any) => new DocumentationAgent(config)
export const createDevOpsAgent = (config?: any) => new DevOpsAgent(config)
export const createSecurityAgent = (config?: any) => new SecurityAgent(config)
export const createMaintenanceAgent = (config?: any) => new MaintenanceAgent(config)
export const createIntegrationAgent = (config?: any) => new IntegrationAgent(config)
export const createAnalyticsAgent = (config?: any) => new AnalyticsAgent(config)
export const createUserInteractionAgent = (config?: any) => new UserInteractionAgent(config)
export const createWorkflowOrchestrator = (config?: any) => new WorkflowOrchestrator(config)
export const createAdvancedWorkflowEngine = (config?: any, coordinationSystems?: any) => new AdvancedWorkflowEngine(config, coordinationSystems)
export const createWorkflowAnalytics = (config?: any) => new WorkflowAnalytics(config)
export const createAgentDiscoveryService = (config?: any) => new AgentDiscoveryService(config)
export const createLoadBalancer = (discoveryService: any, config?: any) => new LoadBalancer(discoveryService, config)
export const createFailoverManager = (discoveryService: any, loadBalancer: any, config?: any) => new FailoverManager(discoveryService, loadBalancer, config)
export const createAPI = (framework?: AG3NTFrameworkClass) => new FrameworkAPI(framework || new AG3NTFrameworkClass())

// Framework metadata
export const FRAMEWORK_VERSION = '1.0.0'
export const FRAMEWORK_NAME = 'AG3NT Framework'
export const FRAMEWORK_DESCRIPTION = 'Sophisticated autonomous agent framework with MCP enhancement'

// Framework capabilities
export const FRAMEWORK_CAPABILITIES = {
  MCP_ENHANCED: true,
  SEQUENTIAL_THINKING: true,
  CONTEXT_ENRICHMENT: true,
  RAG_INTEGRATION: true,
  AGENT_COORDINATION: true,
  PROGRESS_TRACKING: true,
  HEALTH_MONITORING: true,
  DYNAMIC_WORKFLOWS: true,
  REAL_TIME_COMMUNICATION: true,
  MULTI_AGENT_WORKFLOWS: true,
  EVENT_DRIVEN_ARCHITECTURE: true,
  DISTRIBUTED_STATE_MANAGEMENT: true,
  CODEBASE_ANALYSIS: true,
  CONTEXT_CACHING: true,
  MEMORY_MANAGEMENT: true,
  CROSS_REFERENCE_TRACKING: true,
  DECISION_GRAPH_TRACKING: true,
  CONTEXT_INTEGRITY_VALIDATION: true,
  NEO4J_GRAPH_DATABASE: true,
  NATURAL_LANGUAGE_QUERYING: true,
  PERSISTENT_CONTEXT_STORAGE: true
} as const

// Export everything for convenience
export * from './ag3nt-framework'
export * from './core/base-agent'
export * from './core/agent-registry'
export * from './agents/planning-agent'
