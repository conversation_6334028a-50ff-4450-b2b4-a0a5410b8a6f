#!/usr/bin/env node

/**
 * AG3NT Platform - Framework Setup Script
 * 
 * Sets up the AG3NT Framework integration with the platform
 */

const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

console.log('🚀 Setting up AG3NT Framework integration...')

function runCommand(command, description) {
  console.log(`\n${description}...`)
  try {
    execSync(command, { stdio: 'inherit' })
    console.log(`✅ ${description} completed`)
  } catch (error) {
    console.error(`❌ ${description} failed:`, error.message)
    process.exit(1)
  }
}

async function main() {
  try {
    // Check if we're in the right directory
    if (!fs.existsSync('package.json')) {
      throw new Error('Please run this script from the project root directory')
    }

    // Check if framework directory exists
    if (!fs.existsSync('ag3nt-framework-standalone')) {
      throw new Error('AG3NT Framework standalone directory not found')
    }

    console.log('📦 AG3NT Framework Setup')
    console.log('=' .repeat(50))

    // Install framework dependencies
    console.log('\n🔧 Installing framework dependencies...')
    process.chdir('ag3nt-framework-standalone')
    
    runCommand('npm install', '📦 Installing framework packages')
    runCommand('npm run build', '🔨 Building framework')
    
    // Run framework tests to ensure everything works
    try {
      runCommand('npm test', '🧪 Testing framework')
    } catch (error) {
      console.warn('⚠️ Some framework tests failed, but continuing setup...')
    }

    // Go back to project root
    process.chdir('..')

    // Install platform dependencies
    runCommand('npm install', '📦 Installing platform packages')

    // Create symlink for easier imports (optional)
    try {
      if (!fs.existsSync('node_modules/ag3nt-framework')) {
        fs.symlinkSync(
          path.resolve('ag3nt-framework-standalone'),
          path.resolve('node_modules/ag3nt-framework'),
          'dir'
        )
        console.log('🔗 Created framework symlink')
      }
    } catch (error) {
      console.warn('⚠️ Could not create symlink (this is optional):', error.message)
    }

    // Create environment configuration
    console.log('\n⚙️ Setting up environment configuration...')
    
    const envContent = `# AG3NT Framework Configuration
AG3NT_FRAMEWORK_ENABLED=true
AG3NT_ENABLE_MCP=true
AG3NT_ENABLE_COORDINATION=true
AG3NT_ENABLE_DISCOVERY=true
AG3NT_ENABLE_ANALYTICS=true

# Framework Performance
AG3NT_MAX_AGENTS=20
AG3NT_LOAD_BALANCE_ALGORITHM=adaptive
AG3NT_ENABLE_FAILOVER=true

# Development
AG3NT_LOG_LEVEL=info
AG3NT_ENABLE_DEBUG=false
`

    if (!fs.existsSync('.env.local')) {
      fs.writeFileSync('.env.local', envContent)
      console.log('✅ Created .env.local with framework configuration')
    } else {
      console.log('ℹ️ .env.local already exists, skipping environment setup')
    }

    // Verify integration
    console.log('\n🔍 Verifying integration...')
    
    const requiredFiles = [
      'lib/framework-service.ts',
      'hooks/use-framework.ts',
      'app/api/framework/route.ts',
      'components/framework-status.tsx'
    ]

    for (const file of requiredFiles) {
      if (fs.existsSync(file)) {
        console.log(`✅ ${file} - Found`)
      } else {
        console.log(`❌ ${file} - Missing`)
      }
    }

    console.log('\n🎉 Framework integration setup completed!')
    console.log('\n📋 Next steps:')
    console.log('  1. Start the development server: npm run dev:platform')
    console.log('  2. Open http://localhost:3000 in your browser')
    console.log('  3. The framework status will appear in the top-right corner')
    console.log('  4. Try creating a project to test the integration')
    console.log('\n🔧 Framework commands:')
    console.log('  • npm run framework:demo - Run framework demonstrations')
    console.log('  • npm run framework:test - Run framework tests')
    console.log('  • npm run framework:build - Build framework')
    console.log('\n🚀 The AG3NT Framework is now integrated with your platform!')

  } catch (error) {
    console.error('\n❌ Setup failed:', error.message)
    process.exit(1)
  }
}

// Handle Ctrl+C gracefully
process.on('SIGINT', () => {
  console.log('\n\n👋 Setup interrupted')
  process.exit(0)
})

// Run setup
main().catch(console.error)
