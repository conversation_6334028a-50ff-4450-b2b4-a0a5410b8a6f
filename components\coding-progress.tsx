/**
 * AG3NT Platform - Coding Progress Component
 * 
 * Displays real-time progress of autonomous coding workflow
 */

'use client'

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"
import { 
  Code, 
  Database, 
  TestTube, 
  Rocket, 
  CheckCircle, 
  Clock, 
  AlertCircle,
  Loader2,
  Users,
  Zap
} from 'lucide-react'
import { useCoding } from '@/hooks/use-coding'

const taskTypeIcons = {
  frontend: <Code className="h-4 w-4" />,
  backend: <Code className="h-4 w-4" />,
  database: <Database className="h-4 w-4" />,
  testing: <TestTube className="h-4 w-4" />,
  deployment: <Rocket className="h-4 w-4" />
}

const statusIcons = {
  pending: <Clock className="h-4 w-4 text-gray-400" />,
  in_progress: <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />,
  completed: <CheckCircle className="h-4 w-4 text-green-500" />,
  failed: <AlertCircle className="h-4 w-4 text-red-500" />
}

export function CodingProgress() {
  const coding = useCoding()

  if (!coding.isRunning && !coding.progress) {
    return null
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'green'
      case 'in_progress': return 'blue'
      case 'failed': return 'red'
      default: return 'gray'
    }
  }

  const formatTime = (timestamp: number) => {
    const date = new Date(timestamp)
    return date.toLocaleTimeString()
  }

  const formatDuration = (startTime?: number, endTime?: number) => {
    if (!startTime) return ''
    const end = endTime || Date.now()
    const duration = Math.round((end - startTime) / 1000)
    return `${duration}s`
  }

  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Zap className="h-5 w-5 text-blue-500" />
            <CardTitle className="text-lg">Autonomous Coding</CardTitle>
          </div>
          <div className="flex items-center space-x-2">
            {coding.isRunning ? (
              <Badge variant="blue" className="animate-pulse">
                <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                Coding in Progress
              </Badge>
            ) : (
              <Badge variant="green">
                <CheckCircle className="h-3 w-3 mr-1" />
                Completed
              </Badge>
            )}
          </div>
        </div>
        <CardDescription>
          AI agents are autonomously building your project
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Overall Progress */}
        {coding.progress && (
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Overall Progress</span>
              <span className="font-medium">
                {coding.completionPercentage}% ({coding.progress.completedTasks}/{coding.progress.totalTasks})
              </span>
            </div>
            <Progress value={coding.completionPercentage} className="h-2" />
          </div>
        )}

        {/* Current Phase */}
        {coding.progress && (
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Users className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm text-muted-foreground">Current Phase</span>
            </div>
            <Badge variant="outline">
              {coding.progress.currentPhase}
            </Badge>
          </div>
        )}

        {/* Active Agents */}
        {coding.progress && coding.progress.activeAgents.length > 0 && (
          <>
            <Separator />
            <div className="space-y-3">
              <h4 className="text-sm font-medium flex items-center space-x-2">
                <Users className="h-4 w-4" />
                <span>Active Agents ({coding.progress.activeAgents.length})</span>
              </h4>
              
              <div className="grid grid-cols-2 gap-2">
                {coding.progress.activeAgents.map((agent) => (
                  <div key={agent} className="flex items-center space-x-2 text-xs">
                    <div className="w-2 h-2 rounded-full bg-green-500" />
                    <span className="truncate">{agent.replace('-agent', '').replace('-', ' ')}</span>
                  </div>
                ))}
              </div>
            </div>
          </>
        )}

        {/* Task List */}
        {coding.tasks.length > 0 && (
          <>
            <Separator />
            <div className="space-y-3">
              <h4 className="text-sm font-medium">Coding Tasks</h4>
              
              <ScrollArea className="h-64">
                <div className="space-y-2">
                  {coding.tasks.map((task) => (
                    <div key={task.id} className="flex items-center space-x-3 p-2 rounded-lg border">
                      <div className="flex items-center space-x-2">
                        {taskTypeIcons[task.type]}
                        {statusIcons[task.status]}
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <p className="text-sm font-medium truncate">{task.title}</p>
                          <Badge variant={getStatusColor(task.status) as any} className="text-xs">
                            {task.status.replace('_', ' ')}
                          </Badge>
                        </div>
                        <p className="text-xs text-muted-foreground truncate">{task.description}</p>
                        
                        {/* Task timing */}
                        {task.startTime && (
                          <div className="flex items-center space-x-2 text-xs text-muted-foreground mt-1">
                            <span>Started: {formatTime(task.startTime)}</span>
                            {task.endTime && (
                              <span>• Duration: {formatDuration(task.startTime, task.endTime)}</span>
                            )}
                          </div>
                        )}
                        
                        {/* Task output preview */}
                        {task.output && task.status === 'completed' && (
                          <div className="mt-1 text-xs text-green-600">
                            {task.output.files && `${task.output.files.length} files generated`}
                            {task.output.components && ` • ${task.output.components.length} components`}
                            {task.output.endpoints && ` • ${task.output.endpoints.length} endpoints`}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </div>
          </>
        )}

        {/* Statistics */}
        {coding.progress && (
          <>
            <Separator />
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="space-y-1">
                <div className="text-muted-foreground">Completed Tasks</div>
                <div className="font-medium text-green-600">{coding.progress.completedTasks}</div>
              </div>
              <div className="space-y-1">
                <div className="text-muted-foreground">In Progress</div>
                <div className="font-medium text-blue-600">{coding.progress.inProgressTasks}</div>
              </div>
              <div className="space-y-1">
                <div className="text-muted-foreground">Failed Tasks</div>
                <div className="font-medium text-red-600">{coding.progress.failedTasks}</div>
              </div>
              <div className="space-y-1">
                <div className="text-muted-foreground">Estimated Completion</div>
                <div className="font-medium">
                  {coding.progress.estimatedCompletion ? 
                    formatTime(coding.progress.estimatedCompletion) : 
                    'Calculating...'
                  }
                </div>
              </div>
            </div>
          </>
        )}

        {/* Error Display */}
        {coding.hasError && (
          <>
            <Separator />
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-red-600">Error</h4>
              <p className="text-xs text-red-600 bg-red-50 p-2 rounded">
                {coding.status.error}
              </p>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  )
}

export default CodingProgress
